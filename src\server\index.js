import { Server } from 'boardgame.io/server';
import { DailyGrindDash } from '../game/game.js';

const server = Server({
  games: [DailyGrindDash],
  origins: ['http://localhost:3000', 'http://127.0.0.1:3000']
});

const PORT = process.env.PORT || 8000;

server.run(PORT, () => {
  console.log(`Daily Grind Dash server running on port ${PORT}`);
  console.log(`Game server available at http://localhost:${PORT}`);
});
