const { Server } = require('boardgame.io/server');

// Simple game definition for server
const DailyGrindDash = {
  name: 'DailyGrindDash',
  setup: () => ({
    players: {},
    currentPlayer: '0',
    phase: 'setup',
    turn: 0,
    round: 0,
    board: Array.from({ length: 20 }, (_, i) => ({ id: i, name: `Tile ${i}`, type: 'Working Day' })),
    decks: { market: [], sideHustle: [], grandOpportunity: [], gizmo: [] },
    discardPiles: { market: [], sideHustle: [], grandOpportunity: [], gizmo: [] },
    activeDeals: [],
    chatMessages: [],
    gameMode: 'online'
  }),
  moves: {
    joinGame: (G, ctx, playerName, professionName) => {
      G.players[ctx.playerID] = {
        id: ctx.playerID,
        name: playerName,
        profession: { name: professionName, salary: 5000, expenses: 4000, creditLimit: 10000 },
        position: 0,
        cash: 10000,
        passiveIncome: 0,
        expenses: 4000,
        creditLimit: 10000,
        creditUsed: 0,
        loans: [],
        cards: [],
        avatar: `player-${ctx.playerID}`
      };
    },
    rollDice: (G, ctx) => {
      if (ctx.playerID !== G.currentPlayer) return;
      const roll = ctx.random.D6();
      const player = G.players[ctx.playerID];
      player.position = (player.position + roll) % 20;
      G.phase = 'resolve';
      return { roll, newPosition: player.position };
    },
    endTurn: (G, ctx) => {
      if (ctx.playerID !== G.currentPlayer) return;
      const playerIds = Object.keys(G.players);
      const currentIndex = playerIds.indexOf(G.currentPlayer);
      const nextIndex = (currentIndex + 1) % playerIds.length;
      G.currentPlayer = playerIds[nextIndex];
      G.phase = 'roll';
      if (nextIndex === 0) G.round++;
      G.turn++;
    }
  },
  minPlayers: 2,
  maxPlayers: 10
};

const server = Server({
  games: [DailyGrindDash],
  origins: ['http://localhost:3000', 'http://127.0.0.1:3000']
});

const PORT = process.env.PORT || 8001;

server.run(PORT, () => {
  console.log(`Daily Grind Dash server running on port ${PORT}`);
  console.log(`Game server available at http://localhost:${PORT}`);
});
