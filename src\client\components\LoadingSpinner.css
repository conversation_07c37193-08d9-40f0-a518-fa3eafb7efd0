.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.spinner {
  position: relative;
  width: 60px;
  height: 60px;
}

.loading-spinner.small .spinner {
  width: 30px;
  height: 30px;
}

.loading-spinner.large .spinner {
  width: 80px;
  height: 80px;
}

.spinner-ring {
  position: absolute;
  border: 4px solid transparent;
  border-top: 4px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
}

.spinner-ring:nth-child(1) {
  width: 100%;
  height: 100%;
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
  border-top-color: #2196F3;
  animation-delay: -0.3s;
}

.spinner-ring:nth-child(3) {
  width: 60%;
  height: 60%;
  top: 20%;
  left: 20%;
  border-top-color: #FF9800;
  animation-delay: -0.6s;
}

.spinner-ring:nth-child(4) {
  width: 40%;
  height: 40%;
  top: 30%;
  left: 30%;
  border-top-color: #E91E63;
  animation-delay: -0.9s;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-message {
  margin-top: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}
