import { Card } from './types';

// Mini decks for testing (as specified in requirements)
export const GIZMO_CARDS: Card[] = [
  {
    id: 'gizmo-1',
    type: 'Gizmo',
    title: 'Car Repair',
    description: 'Your car broke down unexpectedly',
    expense: 800
  },
  {
    id: 'gizmo-2',
    type: 'Gizmo',
    title: 'Medical Bill',
    description: 'Emergency room visit',
    expense: 1200
  },
  {
    id: 'gizmo-3',
    type: 'Gizmo',
    title: 'Home Appliance',
    description: 'Refrigerator needs replacement',
    expense: 600
  }
];

export const SIDE_HUSTLE_CARDS: Card[] = [
  {
    id: 'side-1',
    type: 'Side Hustle',
    title: 'Food Truck',
    description: 'Start a weekend food truck business',
    cost: 5000,
    income: 400
  },
  {
    id: 'side-2',
    type: 'Side Hustle',
    title: 'Online Course',
    description: 'Create and sell an online course',
    cost: 1000,
    income: 200
  },
  {
    id: 'side-3',
    type: 'Side Hustle',
    title: 'Freelance Design',
    description: 'Start a freelance graphic design service',
    cost: 500,
    income: 300
  },
  {
    id: 'side-4',
    type: 'Side Hustle',
    title: 'Pet Sitting',
    description: 'Neighborhood pet sitting service',
    cost: 200,
    income: 150
  }
];

export const GRAND_OPPORTUNITY_CARDS: Card[] = [
  {
    id: 'grand-1',
    type: 'Grand Opportunity',
    title: 'Rental Property',
    description: 'Purchase a rental duplex',
    cost: 25000,
    income: 1800
  },
  {
    id: 'grand-2',
    type: 'Grand Opportunity',
    title: 'Tech Startup',
    description: 'Invest in a promising tech startup',
    cost: 15000,
    income: 1200
  }
];

export const MARKET_CARDS: Card[] = [
  {
    id: 'market-1',
    type: 'Market',
    title: 'Stock Market Rally',
    description: 'All investments gain 10% value this turn'
  },
  {
    id: 'market-2',
    type: 'Market',
    title: 'Economic Downturn',
    description: 'All investments lose 5% value this turn'
  }
];

// Function to create shuffled decks
export const createShuffledDeck = (cards: Card[]): Card[] => {
  const deck = [...cards];
  for (let i = deck.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [deck[i], deck[j]] = [deck[j], deck[i]];
  }
  return deck;
};

export const createInitialDecks = () => ({
  market: createShuffledDeck(MARKET_CARDS),
  sideHustle: createShuffledDeck(SIDE_HUSTLE_CARDS),
  grandOpportunity: createShuffledDeck(GRAND_OPPORTUNITY_CARDS),
  gizmo: createShuffledDeck(GIZMO_CARDS)
});
