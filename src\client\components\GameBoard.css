.game-board-container {
  width: 100%;
  max-width: 800px;
  margin: 2rem auto;
  padding: 1rem;
}

.board-grid {
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.board-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.board-row:last-child {
  margin-bottom: 0;
}

.board-middle {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  min-height: 300px;
}

.board-column {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.board-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20px;
}

.center-logo {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 2rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.center-logo h2 {
  margin-bottom: 1rem;
  color: white;
  font-size: 1.8rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.center-stats {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
}

.center-stats p {
  margin: 0.25rem 0;
}

.board-tile {
  width: 120px;
  height: 100px;
  background: rgba(255, 255, 255, 0.95);
  border: 3px solid #ddd;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 8px;
  margin: 5px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.board-tile:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.tile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.tile-icon {
  font-size: 1.2rem;
}

.tile-number {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
}

.tile-name {
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
  color: #333;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tile-players {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  justify-content: center;
  min-height: 20px;
}

.player-pawn {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.6rem;
  font-weight: bold;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: pulse 2s infinite;
}

.player-pawn.current-player {
  border-color: #FFD700;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  animation: glow 1.5s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes glow {
  from { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
  to { box-shadow: 0 0 15px rgba(255, 215, 0, 0.8); }
}

/* Tile type specific styling */
.board-tile[style*="border-color: #4CAF50"] {
  background: linear-gradient(135deg, #E8F5E8, #C8E6C9);
}

.board-tile[style*="border-color: #E91E63"] {
  background: linear-gradient(135deg, #FCE4EC, #F8BBD9);
}

.board-tile[style*="border-color: #2196F3"] {
  background: linear-gradient(135deg, #E3F2FD, #BBDEFB);
}

.board-tile[style*="border-color: #FF9800"] {
  background: linear-gradient(135deg, #FFF3E0, #FFCC02);
}

.board-tile[style*="border-color: #9C27B0"] {
  background: linear-gradient(135deg, #F3E5F5, #E1BEE7);
}

.board-tile[style*="border-color: #607D8B"] {
  background: linear-gradient(135deg, #ECEFF1, #CFD8DC);
}

.board-tile[style*="border-color: #F44336"] {
  background: linear-gradient(135deg, #FFEBEE, #FFCDD2);
}

.board-tile[style*="border-color: #795548"] {
  background: linear-gradient(135deg, #EFEBE9, #D7CCC8);
}

/* Responsive design */
@media (max-width: 768px) {
  .game-board-container {
    padding: 0.5rem;
  }
  
  .board-grid {
    padding: 10px;
  }
  
  .board-tile {
    width: 80px;
    height: 70px;
    padding: 4px;
  }
  
  .tile-name {
    font-size: 0.6rem;
  }
  
  .tile-icon {
    font-size: 1rem;
  }
  
  .player-pawn {
    width: 14px;
    height: 14px;
    font-size: 0.5rem;
  }
  
  .center-logo {
    padding: 1rem;
  }
  
  .center-logo h2 {
    font-size: 1.2rem;
  }
  
  .board-middle {
    min-height: 200px;
  }
}

@media (max-width: 480px) {
  .board-tile {
    width: 60px;
    height: 50px;
    padding: 2px;
  }
  
  .tile-name {
    font-size: 0.5rem;
  }
  
  .tile-icon {
    font-size: 0.8rem;
  }
  
  .tile-number {
    width: 16px;
    height: 16px;
    font-size: 0.6rem;
  }
  
  .player-pawn {
    width: 12px;
    height: 12px;
    font-size: 0.4rem;
  }
  
  .center-logo h2 {
    font-size: 1rem;
  }
  
  .center-stats {
    font-size: 0.7rem;
  }
}
