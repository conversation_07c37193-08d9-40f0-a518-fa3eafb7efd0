.chat-component {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 350px;
  height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  border: 1px solid #ddd;
}

.chat-minimized {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.chat-toggle {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 12px 20px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.chat-toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  border-radius: 12px 12px 0 0;
}

.chat-header h3 {
  margin: 0;
  font-size: 1.1rem;
}

.minimize-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.minimize-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.chat-tabs {
  display: flex;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.tab {
  flex: 1;
  padding: 0.75rem 0.5rem;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab:hover {
  background: rgba(76, 175, 80, 0.1);
}

.tab.active {
  background: white;
  border-bottom-color: #4CAF50;
  color: #4CAF50;
}

.deal-selector,
.player-selector {
  padding: 0.5rem;
  background: #f9f9f9;
  border-bottom: 1px solid #eee;
}

.deal-selector select,
.player-selector select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.chat-messages {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  background: #fafafa;
}

.no-messages {
  text-align: center;
  color: #999;
  font-style: italic;
  margin-top: 2rem;
  line-height: 1.5;
}

.message {
  margin-bottom: 1rem;
  animation: fadeIn 0.3s ease;
}

.message.own {
  text-align: right;
}

.message.other {
  text-align: left;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
  font-size: 0.75rem;
  color: #666;
}

.message.own .message-header {
  flex-direction: row-reverse;
}

.sender-name {
  font-weight: 600;
}

.timestamp {
  opacity: 0.7;
}

.message-content {
  background: white;
  padding: 0.75rem;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: inline-block;
  max-width: 80%;
  word-wrap: break-word;
  line-height: 1.4;
}

.message.own .message-content {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
}

.chat-input {
  display: flex;
  padding: 1rem;
  background: white;
  border-top: 1px solid #eee;
  border-radius: 0 0 12px 12px;
  gap: 0.5rem;
}

.chat-input textarea {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0.5rem;
  resize: none;
  font-family: inherit;
  font-size: 0.9rem;
  line-height: 1.4;
}

.chat-input textarea:focus {
  outline: none;
  border-color: #4CAF50;
}

.chat-input textarea:disabled {
  background: #f5f5f5;
  color: #999;
}

.send-btn {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-end;
}

.send-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.send-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar styling */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .chat-component {
    width: 300px;
    height: 400px;
    bottom: 10px;
    right: 10px;
  }
  
  .chat-minimized {
    bottom: 10px;
    right: 10px;
  }
  
  .tab {
    font-size: 0.75rem;
    padding: 0.5rem 0.25rem;
  }
  
  .message-content {
    max-width: 90%;
  }
}

@media (max-width: 480px) {
  .chat-component {
    width: calc(100vw - 20px);
    height: 350px;
    bottom: 10px;
    right: 10px;
    left: 10px;
  }
  
  .chat-header h3 {
    font-size: 1rem;
  }
  
  .tab {
    font-size: 0.7rem;
  }
}
