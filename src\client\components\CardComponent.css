.card-component {
  margin: 0.5rem;
}

.card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 3px solid #ddd;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 250px;
  max-width: 300px;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--card-color, #ddd), transparent);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.card-icon {
  font-size: 1.5rem;
}

.card-type {
  background: rgba(0, 0, 0, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.card-content {
  margin-bottom: 1rem;
}

.card-title {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #333;
  line-height: 1.3;
}

.card-description {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
  margin-bottom: 1rem;
}

.card-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
}

.stat-label {
  font-weight: 600;
  font-size: 0.85rem;
  color: #555;
}

.stat-value {
  font-weight: 700;
  font-size: 0.9rem;
}

.stat.cost .stat-value {
  color: #F44336;
}

.stat.income .stat-value {
  color: #4CAF50;
}

.stat.expense .stat-value {
  color: #FF9800;
}

.card-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.action-btn {
  flex: 1;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.show-btn {
  background: linear-gradient(45deg, #2196F3, #21CBF3);
  color: white;
}

.deal-btn {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Card type specific styling */
.card.market {
  --card-color: #2196F3;
  border-color: #2196F3;
}

.card.side-hustle {
  --card-color: #FF9800;
  border-color: #FF9800;
}

.card.grand-opportunity {
  --card-color: #9C27B0;
  border-color: #9C27B0;
}

.card.gizmo {
  --card-color: #607D8B;
  border-color: #607D8B;
}

/* Deal Modal Styles */
.deal-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.deal-modal {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.deal-type-selector {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.deal-type-selector label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-weight: 600;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
}

.split-form h4 {
  margin-bottom: 1rem;
  color: #333;
}

.split-player {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.split-player label {
  flex: 1;
  font-weight: 600;
}

.split-player input {
  width: 80px;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
}

.split-total {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
  font-weight: 700;
  font-size: 1.1rem;
}

.split-total .error {
  color: #F44336;
  font-weight: 600;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.cancel-btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  color: #333;
  cursor: pointer;
  font-weight: 600;
}

.create-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  cursor: pointer;
  font-weight: 600;
}

.create-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.cancel-btn:hover {
  background: #f5f5f5;
}

.create-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
  .card {
    min-width: 200px;
    max-width: 250px;
  }

  .deal-modal {
    padding: 1rem;
    width: 95%;
  }

  .modal-actions {
    flex-direction: column;
  }

  .split-player {
    flex-direction: column;
    align-items: stretch;
    gap: 0.25rem;
  }

  .split-player input {
    width: 100%;
  }
}
