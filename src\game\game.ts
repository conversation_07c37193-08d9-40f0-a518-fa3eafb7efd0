import { Game } from 'boardgame.io';
import { GameState, Player, Deal, ChatMessage } from './types';
import { PROFESSIONS, BOARD_TILES, MAX_PLAYERS, VICTORY_CONDITION } from './constants';
import { createInitialDecks } from './cards';

// Setup function - initializes the game state
const setup = (ctx: any): GameState => {
  const decks = createInitialDecks();
  
  return {
    players: {},
    currentPlayer: '0',
    phase: 'setup',
    turn: 0,
    round: 0,
    board: BOARD_TILES,
    decks,
    discardPiles: {
      market: [],
      sideHustle: [],
      grandOpportunity: [],
      gizmo: []
    },
    activeDeals: [],
    chatMessages: [],
    gameMode: 'online'
  };
};

// Game moves
const moves = {
  // Join game with profession selection
  joinGame: (G: GameState, ctx: any, playerName: string, professionName: string) => {
    const profession = PROFESSIONS.find(p => p.name === professionName);
    if (!profession) return;

    const player: Player = {
      id: ctx.playerID,
      name: playerName,
      profession,
      position: 0,
      cash: profession.salary * 2, // Start with 2 months salary
      passiveIncome: 0,
      expenses: profession.expenses,
      creditLimit: profession.creditLimit,
      creditUsed: 0,
      loans: [],
      cards: [],
      avatar: `player-${ctx.playerID}`
    };

    G.players[ctx.playerID] = player;
  },

  // Roll dice (online mode)
  rollDice: (G: GameState, ctx: any) => {
    if (ctx.playerID !== G.currentPlayer) return;
    
    const roll = ctx.random.D6();
    const player = G.players[ctx.playerID];
    const newPosition = (player.position + roll) % BOARD_TILES.length;
    
    player.position = newPosition;
    G.phase = 'resolve';
    
    return { roll, newPosition };
  },

  // Select tile (local mode)
  selectTile: (G: GameState, ctx: any, tileId: number) => {
    if (ctx.playerID !== G.currentPlayer) return;
    
    const player = G.players[ctx.playerID];
    player.position = tileId;
    G.phase = 'resolve';
  },

  // Draw card from specified deck
  drawCard: (G: GameState, ctx: any, deckType: keyof GameState['decks']) => {
    if (ctx.playerID !== G.currentPlayer) return;
    
    const deck = G.decks[deckType];
    if (deck.length === 0) {
      // Reshuffle discard pile if deck is empty
      G.decks[deckType] = ctx.random.Shuffle(G.discardPiles[deckType]);
      G.discardPiles[deckType] = [];
    }
    
    const card = G.decks[deckType].pop();
    if (card) {
      G.players[ctx.playerID].cards.push(card);
      return card;
    }
  },

  // Send chat message
  sendMessage: (G: GameState, ctx: any, type: 'global' | 'deal' | 'direct', message: string, recipientId?: string, dealId?: string) => {
    const chatMessage: ChatMessage = {
      id: `msg-${Date.now()}-${ctx.playerID}`,
      type,
      senderId: ctx.playerID,
      recipientId,
      dealId,
      message,
      timestamp: Date.now()
    };
    
    G.chatMessages.push(chatMessage);
  },

  // End turn
  endTurn: (G: GameState, ctx: any) => {
    if (ctx.playerID !== G.currentPlayer) return;
    
    // Apply income and expenses
    const player = G.players[ctx.playerID];
    const netIncome = player.profession.salary + player.passiveIncome - player.expenses;
    player.cash += netIncome;
    
    // Check victory condition
    if (VICTORY_CONDITION(player.passiveIncome, player.expenses)) {
      G.winner = ctx.playerID;
      G.phase = 'victory';
      return;
    }
    
    // Move to next player
    const playerIds = Object.keys(G.players);
    const currentIndex = playerIds.indexOf(G.currentPlayer);
    const nextIndex = (currentIndex + 1) % playerIds.length;
    
    G.currentPlayer = playerIds[nextIndex];
    G.phase = 'roll';
    
    if (nextIndex === 0) {
      G.round++;
    }
    G.turn++;
  }
};

// Game phases
const phases = {
  setup: {
    moves: { joinGame: moves.joinGame },
    start: true,
    next: 'roll'
  },
  roll: {
    moves: { 
      rollDice: moves.rollDice, 
      selectTile: moves.selectTile,
      sendMessage: moves.sendMessage 
    },
    next: 'resolve'
  },
  resolve: {
    moves: { 
      drawCard: moves.drawCard,
      sendMessage: moves.sendMessage,
      endTurn: moves.endTurn 
    },
    next: 'roll'
  },
  victory: {
    moves: { sendMessage: moves.sendMessage }
  }
};

// Main game definition
export const DailyGrindDash: Game<GameState> = {
  name: 'DailyGrindDash',
  setup,
  moves,
  phases,
  minPlayers: 2,
  maxPlayers: MAX_PLAYERS,
  
  turn: {
    order: {
      first: () => 0,
      next: (G: GameState, ctx: any) => {
        const playerIds = Object.keys(G.players);
        const currentIndex = playerIds.indexOf(ctx.playerID);
        return (currentIndex + 1) % playerIds.length;
      }
    }
  },

  endIf: (G: GameState) => {
    if (G.winner) {
      return { winner: G.winner };
    }
  }
};
