// Game Types for Daily Grind Dash

export interface Player {
  id: string;
  name: string;
  profession: Profession;
  position: number; // Board position (0-19)
  cash: number;
  passiveIncome: number;
  expenses: number;
  creditLimit: number;
  creditUsed: number;
  loans: Loan[];
  cards: Card[];
  avatar: string;
}

export interface Profession {
  name: 'Teacher' | 'Engineer' | 'Nurse' | 'Sales Rep' | 'Artist' | 'Mechanic';
  salary: number;
  expenses: number;
  creditLimit: number;
}

export interface Loan {
  id: string;
  amount: number;
  interestRate: number; // APR as decimal (0.07 = 7%)
  monthlyPayment: number;
}

export interface Card {
  id: string;
  type: 'Market' | 'Side Hustle' | 'Grand Opportunity' | 'Gizmo';
  title: string;
  description: string;
  cost?: number;
  income?: number;
  expense?: number;
  isOwned?: boolean;
  ownerId?: string;
}

export interface Deal {
  id: string;
  cardId: string;
  ownerId: string;
  type: 'buyout' | 'split';
  buyoutOffer?: {
    amount: number;
    commission?: number;
  };
  splitOffer?: {
    shares: { [playerId: string]: number }; // Percentages that must sum to 100
  };
  status: 'pending' | 'accepted' | 'rejected' | 'countered';
  approvals: { [playerId: string]: boolean };
}

export interface ChatMessage {
  id: string;
  type: 'global' | 'deal' | 'direct';
  senderId: string;
  recipientId?: string; // For direct messages
  dealId?: string; // For deal-specific chats
  message: string;
  timestamp: number;
}

export interface GameState {
  players: { [id: string]: Player };
  currentPlayer: string;
  phase: 'setup' | 'roll' | 'move' | 'resolve' | 'deal' | 'income' | 'victory';
  turn: number;
  round: number;
  board: BoardTile[];
  decks: {
    market: Card[];
    sideHustle: Card[];
    grandOpportunity: Card[];
    gizmo: Card[];
  };
  discardPiles: {
    market: Card[];
    sideHustle: Card[];
    grandOpportunity: Card[];
    gizmo: Card[];
  };
  activeDeals: Deal[];
  chatMessages: ChatMessage[];
  gameMode: 'local' | 'online';
  winner?: string;
}

export interface BoardTile {
  id: number;
  name: string;
  type: 'Working Day' | 'Give Back' | 'Market' | 'Side Hustles' | 'Grand Opportunities' | 'Gizmos' | 'Job Shake-Up' | 'Life Split';
  description: string;
}

export interface GameMoves {
  rollDice: () => void;
  selectTile: (tileId: number) => void;
  drawCard: (deckType: string) => void;
  showCard: (cardId: string, recipients: string[]) => void;
  createDeal: (cardId: string, dealType: 'buyout' | 'split', offer: any) => void;
  respondToDeal: (dealId: string, response: 'accept' | 'reject' | 'counter', counterOffer?: any) => void;
  sendMessage: (type: 'global' | 'deal' | 'direct', message: string, recipientId?: string, dealId?: string) => void;
  endTurn: () => void;
}
