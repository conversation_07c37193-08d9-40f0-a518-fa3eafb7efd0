.game-client {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.game-setup {
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  margin: 2rem 0;
}

.setup-options {
  display: flex;
  gap: 2rem;
  justify-content: center;
  flex-wrap: wrap;
  margin: 2rem 0;
}

.setup-option {
  background: rgba(255, 255, 255, 0.03);
  padding: 1.5rem;
  border-radius: 8px;
  min-width: 250px;
}

.setup-option h3 {
  margin-bottom: 1rem;
}

.setup-option input {
  width: 100%;
  padding: 0.75rem;
  margin-bottom: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: inherit;
}

.setup-button {
  padding: 0.75rem 1.5rem;
  border: 2px solid #4CAF50;
  border-radius: 6px;
  background: rgba(76, 175, 80, 0.1);
  color: inherit;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.setup-button:hover:not(:disabled) {
  background: rgba(76, 175, 80, 0.2);
  transform: translateY(-2px);
}

.setup-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.back-button {
  margin-top: 2rem;
  padding: 0.5rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  background: transparent;
  color: inherit;
  cursor: pointer;
}

.join-form {
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  max-width: 500px;
  margin: 2rem auto;
}

.form-group {
  margin-bottom: 1.5rem;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: inherit;
  font-size: 1rem;
}

.join-button {
  padding: 1rem 2rem;
  border: 2px solid #2196F3;
  border-radius: 6px;
  background: rgba(33, 150, 243, 0.1);
  color: inherit;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.join-button:hover:not(:disabled) {
  background: rgba(33, 150, 243, 0.2);
  transform: translateY(-2px);
}

.join-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.game-board-wrapper {
  padding: 1rem;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.toggle-cards-btn {
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-cards-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.game-info {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  opacity: 0.8;
}

.game-content {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.player-info,
.board-section,
.other-players {
  background: rgba(255, 255, 255, 0.05);
  padding: 1.5rem;
  border-radius: 8px;
}

.player-card {
  background: rgba(255, 255, 255, 0.03);
  padding: 1rem;
  border-radius: 6px;
  margin-top: 1rem;
}

.player-card h4 {
  margin-bottom: 0.5rem;
  color: #4CAF50;
}

.player-card p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

.progress-to-victory {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.current-tile {
  background: rgba(255, 255, 255, 0.03);
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
}

.current-tile h4 {
  margin-bottom: 0.5rem;
  color: #2196F3;
}

.game-actions {
  text-align: center;
}

.action-button {
  padding: 1rem 2rem;
  border: none;
  border-radius: 6px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0.5rem;
}

.roll-button {
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  color: white;
}

.end-turn-button {
  background: linear-gradient(45deg, #4ECDC4, #44A08D);
  color: white;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.draw-button {
  background: linear-gradient(45deg, #9C27B0, #E91E63);
  color: white;
}

.select-button {
  background: linear-gradient(45deg, #FF9800, #F57C00);
  color: white;
}

.local-mode-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  text-align: center;
}

.local-mode-actions select {
  padding: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  min-width: 200px;
}

.local-mode-actions select option {
  background: #333;
  color: white;
}

.working-day-info,
.give-back-info {
  text-align: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  margin: 1rem 0;
}

.cards-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.cards-modal {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
  color: #333;
}

.cards-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.cards-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  max-height: 60vh;
  overflow-y: auto;
}

.waiting-message {
  text-align: center;
  font-style: italic;
  opacity: 0.7;
  margin: 1rem 0;
}

.other-player {
  background: rgba(255, 255, 255, 0.03);
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border-left: 3px solid transparent;
}

.other-player.current-turn {
  border-left-color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
}

.other-player h4 {
  margin-bottom: 0.5rem;
}

.other-player p {
  margin: 0.25rem 0;
  font-size: 0.85rem;
  opacity: 0.9;
}

.victory-screen {
  text-align: center;
  padding: 3rem;
  background: linear-gradient(45deg, #4CAF50, #45a049);
  border-radius: 12px;
  color: white;
  margin: 2rem 0;
}

.victory-screen h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.victory-screen h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.final-stats {
  margin-top: 2rem;
  text-align: left;
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 8px;
}

.player-final-stats {
  background: rgba(255, 255, 255, 0.05);
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.player-final-stats.winner {
  background: rgba(255, 215, 0, 0.2);
  border: 2px solid gold;
}

.player-final-stats h4 {
  margin-bottom: 0.5rem;
  color: #FFD700;
}

.player-final-stats p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .game-content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .setup-options {
    flex-direction: column;
    align-items: center;
  }
  
  .victory-screen h1 {
    font-size: 2rem;
  }
  
  .victory-screen h2 {
    font-size: 1.5rem;
  }
}
