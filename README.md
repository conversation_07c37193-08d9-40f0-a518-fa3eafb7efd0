# Daily Grind Dash

A browser-based multiplayer board game about financial literacy, built with React, TypeScript, and boardgame.io.

## Features

- **Dual Game Modes**: Local (physical board + digital money/cards) and Online (fully digital)
- **Up to 10 Players**: Multiplayer support for large groups
- **Progressive Web App**: Works on phones, tablets, and laptops with offline caching
- **Real-time Multiplayer**: WebSocket-based networking via boardgame.io
- **Financial Literacy**: Learn about passive income, expenses, and investment strategies

## Quick Start

### Development

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Start the development server**:
   ```bash
   npm run dev
   ```

3. **Start the game server** (in a separate terminal):
   ```bash
   npm run server
   ```

4. **Open your browser** to `http://localhost:3000`

### Building for Production

```bash
npm run build
npm run preview
```

## Game Overview

### Victory Condition
Be the first player whose **passive income ≥ expenses**.

### Professions
Choose from 6 starting professions, each with different salary, expenses, and credit limits:
- Teacher
- Engineer  
- Nurse
- Sales Rep
- Artist
- Mechanic

### Board Tiles
Navigate a 20-tile board with 8 different event types:
- **Working Day**: Collect salary & pay bills
- **Give Back**: Donate income for bonus dice
- **Market**: Economic events affect investments
- **Side Hustles**: Small investment opportunities
- **Grand Opportunities**: Large investment deals
- **Gizmos**: Unexpected expenses
- **Job Shake-Up**: Miss turns, pay fixed charges
- **Life Split**: Major life changes

### Card Types
- **Market Cards**: Economic events (2 cards)
- **Side Hustles**: Small investments (4 cards)
- **Grand Opportunities**: Large investments (2 cards)
- **Gizmos**: Unexpected expenses (3 cards)

## Project Structure

```
/src
  /game        ← boardgame.io logic
  /client      ← React PWA
  /server      ← Node WebSocket server
/docs
  GDD.md       ← Game Design Document
  API.md       ← Move/state contracts
/storybook     ← UI component showcase
```

## Technology Stack

- **Frontend**: React + TypeScript + Vite
- **Game Engine**: boardgame.io
- **Backend**: Node.js WebSocket server
- **PWA**: Service Worker + Web App Manifest
- **Future**: Supabase for persistence

## Development Roadmap

### V1 (Current)
- [x] Basic game setup and structure
- [x] Core gameplay loop
- [x] 6 professions and mini card decks
- [x] 20-tile board
- [ ] Deal and offer system
- [ ] Chat system
- [ ] PWA features
- [ ] Storybook components

### V2 (Future)
- [ ] Game persistence and resume
- [ ] Dynamic inflation (BLS CPI API)
- [ ] Multi-card credit system
- [ ] Accessibility features
- [ ] Isometric board skin

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

ISC License
