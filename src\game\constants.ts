import { Profession, BoardTile, Card } from './types';

// Starting professions with 2025 dollar values
export const PROFESSIONS: Profession[] = [
  {
    name: 'Teacher',
    salary: 4200, // $50,400/year
    expenses: 3800,
    creditLimit: 8000
  },
  {
    name: 'Engineer',
    salary: 7500, // $90,000/year
    expenses: 6200,
    creditLimit: 15000
  },
  {
    name: 'Nurse',
    salary: 6000, // $72,000/year
    expenses: 4800,
    creditLimit: 12000
  },
  {
    name: 'Sales Rep',
    salary: 5500, // $66,000/year
    expenses: 4500,
    creditLimit: 11000
  },
  {
    name: 'Artist',
    salary: 3200, // $38,400/year
    expenses: 3000,
    creditLimit: 6000
  },
  {
    name: 'Mechanic',
    salary: 4800, // $57,600/year
    expenses: 4200,
    creditLimit: 9000
  }
];

// 20-tile board loop with 8 different event types
export const BOARD_TILES: BoardTile[] = [
  { id: 0, name: 'Working Day', type: 'Working Day', description: 'Collect salary & pay automatic bills' },
  { id: 1, name: '<PERSON> Hustles', type: 'Side Hustles', description: 'Draw a small-deal card' },
  { id: 2, name: 'Market', type: 'Market', description: 'Draw a Market card (prices rise/fall)' },
  { id: 3, name: 'Give Back', type: 'Give Back', description: 'May donate % income to roll 3 dice next turn' },
  { id: 4, name: 'Grand Opportunities', type: 'Grand Opportunities', description: 'Draw a big-deal card' },
  { id: 5, name: 'Working Day', type: 'Working Day', description: 'Collect salary & pay automatic bills' },
  { id: 6, name: 'Gizmos', type: 'Gizmos', description: 'Unexpected expenses' },
  { id: 7, name: 'Side Hustles', type: 'Side Hustles', description: 'Draw a small-deal card' },
  { id: 8, name: 'Job Shake-Up', type: 'Job Shake-Up', description: 'Miss two turns, pay fixed charges' },
  { id: 9, name: 'Market', type: 'Market', description: 'Draw a Market card (prices rise/fall)' },
  { id: 10, name: 'Working Day', type: 'Working Day', description: 'Collect salary & pay automatic bills' },
  { id: 11, name: 'Grand Opportunities', type: 'Grand Opportunities', description: 'Draw a big-deal card' },
  { id: 12, name: 'Give Back', type: 'Give Back', description: 'May donate % income to roll 3 dice next turn' },
  { id: 13, name: 'Side Hustles', type: 'Side Hustles', description: 'Draw a small-deal card' },
  { id: 14, name: 'Life Split', type: 'Life Split', description: 'Halve cash, adjust expenses' },
  { id: 15, name: 'Working Day', type: 'Working Day', description: 'Collect salary & pay automatic bills' },
  { id: 16, name: 'Gizmos', type: 'Gizmos', description: 'Unexpected expenses' },
  { id: 17, name: 'Market', type: 'Market', description: 'Draw a Market card (prices rise/fall)' },
  { id: 18, name: 'Grand Opportunities', type: 'Grand Opportunities', description: 'Draw a big-deal card' },
  { id: 19, name: 'Side Hustles', type: 'Side Hustles', description: 'Draw a small-deal card' }
];

// Interest rates for 2025 (hard-coded averages)
export const INTEREST_RATES = {
  CREDIT_CARD_APR: 0.22, // 22% APR
  REAL_ESTATE_LOAN_APR: 0.07 // 7% APR
};

// Victory condition
export const VICTORY_CONDITION = (passiveIncome: number, expenses: number): boolean => {
  return passiveIncome >= expenses;
};

// Maximum players per room
export const MAX_PLAYERS = 10;
