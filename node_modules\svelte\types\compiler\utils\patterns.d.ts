export declare const regex_whitespace: RegExp;
export declare const regex_whitespaces: RegExp;
export declare const regex_starts_with_whitespace: RegExp;
export declare const regex_starts_with_whitespaces: RegExp;
export declare const regex_ends_with_whitespace: RegExp;
export declare const regex_ends_with_whitespaces: RegExp;
export declare const regex_only_whitespaces: RegExp;
export declare const regex_whitespace_characters: RegExp;
export declare const regex_non_whitespace_character: RegExp;
export declare const regex_starts_with_newline: RegExp;
export declare const regex_not_newline_characters: RegExp;
export declare const regex_double_quotes: RegExp;
export declare const regex_backslashes: RegExp;
export declare const regex_starts_with_underscore: RegExp;
export declare const regex_ends_with_underscore: RegExp;
export declare const regex_invalid_variable_identifier_characters: RegExp;
export declare const regex_dimensions: RegExp;
export declare const regex_content_rect: RegExp;
export declare const regex_content_box_size: RegExp;
export declare const regex_border_box_size: RegExp;
export declare const regex_device_pixel_content_box_size: RegExp;
export declare const regex_box_size: RegExp;
