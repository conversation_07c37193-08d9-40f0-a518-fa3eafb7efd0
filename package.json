{"name": "daily-grind-dash", "version": "1.0.0", "type": "module", "description": "Daily Grind Dash - A browser-based multiplayer board game about financial literacy", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "server": "node src/server/index.js", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["boardgame", "react", "typescript", "multiplayer", "financial-literacy"], "author": "", "license": "ISC", "dependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "boardgame.io": "^0.50.2", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.8.3", "vite": "^7.0.2"}, "devDependencies": {"@types/node": "^24.0.10"}}