# Daily Grind Dash - Game Design Document

## High-Level Vision
Everyone joins the same browser-based lobby (phone, tablet, or laptop). In Local Mode the physical board and dice stay on the table; each device only handles money, cards, offers, and approvals. In Online Mode a 2-D board, built with boardgame.io, renders pawns and rolls. The winner (for v1) is the first player whose passive income ≥ expenses. Up to ten players are supported per room.

## Core Gameplay Loop

| Phase | Trigger | What the Device Does |
|-------|---------|---------------------|
| Roll & Move | Local → players roll physical dice; Online → tap ROLL DICE (boardgame.io RNG) | If Online, pawn animates to tile; if Local, player taps Landed On and selects tile name from searchable list. |
| Resolve Tile | See tile list below | App privately shows any card; other players see a flashing badge beside that player's avatar. |
| Deals & Offers | On Side Hustle / Grand Opportunity cards | Owner can Show to All, then tap Send Offer → choose Buy Out or Split Deal. Split UI displays 100% pie; players enter % shares, see real-time totals, and submit. Counters force a fresh approval from all investors. |
| Income / Expense Tick | End of round | Device auto-adjusts cashflow, applies loan interest, credit-card payments. |
| Victory Check | End of each full turn | If passive ≥ expense: declare winner, archive session. |

## Tiles & Events (V1)

| Board Tile | New Name / Function |
|------------|-------------------|
| Payday | Working Day – collect salary & pay automatic bills. |
| Charity | Give Back – may donate X% income to roll 3 dice next turn. |
| Market | Market – draw Market card (prices rise/fall). |
| Side Hustles | Side Hustles – draw small-deal card. |
| Grand Opportunities | Grand Opportunities – draw big-deal card. |
| Gizmos / Doohickeys | Gizmos – unexpected expenses. |
| Downsized | Job Shake-Up – miss two turns, pay fixed charges. |
| Divorced | Life Split – halve cash, adjust expenses. |

## Victory Condition
The first player whose passive income ≥ expenses wins the game.

## Technology Stack
- **Client**: React + TypeScript + Vite PWA
- **Game Logic**: boardgame.io
- **Server**: Node.js WebSocket server
- **Future**: Supabase for persistence and real-time features

## Testing Assets (V1)
- 6 Professions: Teacher, Engineer, Nurse, Sales Rep, Artist, Mechanic
- Mini Decks: 3 Gizmos, 4 Side Hustles, 2 Grand Opportunities, 2 Market events
- Test Board: 20-tile loop repeating the eight event spaces
