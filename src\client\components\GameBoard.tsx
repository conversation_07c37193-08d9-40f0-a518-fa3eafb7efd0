import React from 'react';
import { GameState } from '../../game/types';
import './GameBoard.css';

interface GameBoardProps {
  G: GameState;
  playerID: string;
}

const GameBoard: React.FC<GameBoardProps> = ({ G, playerID }) => {
  const renderTile = (tileIndex: number) => {
    const tile = G.board[tileIndex];
    const playersOnTile = Object.values(G.players).filter(p => p.position === tileIndex);
    
    const getTileIcon = (type: string) => {
      switch (type) {
        case 'Working Day': return '💼';
        case 'Give Back': return '❤️';
        case 'Market': return '📈';
        case 'Side Hustles': return '💡';
        case 'Grand Opportunities': return '🏆';
        case 'Gizmos': return '⚙️';
        case 'Job Shake-Up': return '⚡';
        case 'Life Split': return '💔';
        default: return '❓';
      }
    };

    const getTileColor = (type: string) => {
      switch (type) {
        case 'Working Day': return '#4CAF50';
        case 'Give Back': return '#E91E63';
        case 'Market': return '#2196F3';
        case 'Side Hustles': return '#FF9800';
        case 'Grand Opportunities': return '#9C27B0';
        case 'Gizmos': return '#607D8B';
        case 'Job Shake-Up': return '#F44336';
        case 'Life Split': return '#795548';
        default: return '#9E9E9E';
      }
    };

    return (
      <div 
        key={tileIndex}
        className={`board-tile tile-${tileIndex}`}
        style={{ borderColor: getTileColor(tile.type) }}
      >
        <div className="tile-header">
          <span className="tile-icon">{getTileIcon(tile.type)}</span>
          <span className="tile-number">{tileIndex}</span>
        </div>
        <div className="tile-name">{tile.name}</div>
        <div className="tile-players">
          {playersOnTile.map((player, idx) => (
            <div 
              key={player.id}
              className={`player-pawn ${player.id === playerID ? 'current-player' : ''}`}
              style={{ backgroundColor: getPlayerColor(player.id) }}
              title={player.name}
            >
              {player.name.charAt(0).toUpperCase()}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const getPlayerColor = (playerId: string) => {
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'];
    const index = parseInt(playerId) % colors.length;
    return colors[index];
  };

  return (
    <div className="game-board-container">
      <div className="board-grid">
        {/* Top row (tiles 0-4) */}
        <div className="board-row top-row">
          {[0, 1, 2, 3, 4].map(renderTile)}
        </div>
        
        {/* Middle rows with side tiles */}
        <div className="board-middle">
          <div className="board-column left-column">
            {[19, 18, 17, 16, 15].map(renderTile)}
          </div>
          
          <div className="board-center">
            <div className="center-logo">
              <h2>Daily Grind Dash</h2>
              <div className="center-stats">
                <p>Round: {G.round}</p>
                <p>Turn: {G.turn}</p>
                <p>Players: {Object.keys(G.players).length}</p>
              </div>
            </div>
          </div>
          
          <div className="board-column right-column">
            {[5, 6, 7, 8, 9].map(renderTile)}
          </div>
        </div>
        
        {/* Bottom row (tiles 10-14) */}
        <div className="board-row bottom-row">
          {[14, 13, 12, 11, 10].map(renderTile)}
        </div>
      </div>
    </div>
  );
};

export default GameBoard;
