.app {
  min-height: 100vh;
  background: inherit;
}

.app-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.app-header {
  text-align: center;
  padding: 3rem 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeIn 1s ease-out;
}

.app-header h1 {
  font-size: 4rem;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #FFD700, #FFA500, #FF6B6B, #4ECDC4);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  font-weight: 800;
  letter-spacing: -2px;
}

.app-header p {
  font-size: 1.3rem;
  opacity: 0.9;
  margin-bottom: 0;
  font-weight: 500;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.lobby {
  text-align: center;
  padding: 4rem 2rem;
  animation: slideIn 0.8s ease-out;
}

.lobby h2 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: 700;
}

.lobby > p {
  font-size: 1.2rem;
  margin-bottom: 3rem;
  opacity: 0.9;
  font-weight: 500;
}

.mode-buttons {
  display: flex;
  gap: 3rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 3rem;
}

.mode-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 3rem 2rem;
  border: 3px solid transparent;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: inherit;
  font-size: 1.6rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  min-width: 320px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.mode-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.mode-button:hover::before {
  left: 100%;
}

.mode-button:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.mode-button.local {
  border-color: #4CAF50;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.1));
}

.mode-button.local:hover {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.2));
  border-color: #66BB6A;
  box-shadow: 0 15px 35px rgba(76, 175, 80, 0.4);
}

.mode-button.online {
  border-color: #2196F3;
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.2), rgba(33, 150, 243, 0.1));
}

.mode-button.online:hover {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.3), rgba(33, 150, 243, 0.2));
  border-color: #42A5F5;
  box-shadow: 0 15px 35px rgba(33, 150, 243, 0.4);
}

.mode-description {
  font-size: 1rem;
  font-weight: 500;
  opacity: 0.8;
  margin-top: 1rem;
  line-height: 1.5;
  max-width: 280px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.game-mode {
  text-align: center;
  padding: 2rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
}

.game-mode h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.game-mode button {
  margin-top: 2rem;
  padding: 0.75rem 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  background: transparent;
  color: inherit;
  cursor: pointer;
  transition: all 0.3s ease;
}

.game-mode button:hover {
  background: rgba(255, 255, 255, 0.1);
}

@media (max-width: 768px) {
  .mode-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .mode-button {
    min-width: 280px;
  }
  
  .app-header h1 {
    font-size: 2rem;
  }
}
