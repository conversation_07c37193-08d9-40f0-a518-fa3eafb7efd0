.app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.app-header {
  margin-bottom: 3rem;
}

.app-header h1 {
  font-size: 3rem;
  margin-bottom: 0.5rem;
  background: linear-gradient(45deg, #4CAF50, #2196F3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.app-header p {
  font-size: 1.2rem;
  opacity: 0.8;
}

.lobby {
  text-align: center;
}

.lobby h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.mode-buttons {
  display: flex;
  gap: 2rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 2rem;
}

.mode-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  border: 2px solid transparent;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  color: inherit;
  font-size: 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 300px;
  text-align: center;
}

.mode-button:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.mode-button.local {
  border-color: #4CAF50;
}

.mode-button.local:hover {
  background: rgba(76, 175, 80, 0.1);
}

.mode-button.online {
  border-color: #2196F3;
}

.mode-button.online:hover {
  background: rgba(33, 150, 243, 0.1);
}

.mode-description {
  font-size: 0.9rem;
  font-weight: 400;
  opacity: 0.7;
  margin-top: 0.5rem;
  line-height: 1.4;
}

.game-mode {
  text-align: center;
  padding: 2rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
}

.game-mode h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.game-mode button {
  margin-top: 2rem;
  padding: 0.75rem 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  background: transparent;
  color: inherit;
  cursor: pointer;
  transition: all 0.3s ease;
}

.game-mode button:hover {
  background: rgba(255, 255, 255, 0.1);
}

@media (max-width: 768px) {
  .mode-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .mode-button {
    min-width: 280px;
  }
  
  .app-header h1 {
    font-size: 2rem;
  }
}
