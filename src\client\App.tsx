import React, { useState } from 'react'
import './App.css'

function App() {
  const [gameMode, setGameMode] = useState<'lobby' | 'local' | 'online'>('lobby')

  return (
    <div className="app">
      <header className="app-header">
        <h1>Daily Grind Dash</h1>
        <p>A browser-based multiplayer board game about financial literacy</p>
      </header>
      
      <main className="app-main">
        {gameMode === 'lobby' && (
          <div className="lobby">
            <h2>Welcome to Daily Grind Dash!</h2>
            <p>Choose your game mode:</p>
            <div className="mode-buttons">
              <button 
                className="mode-button local"
                onClick={() => setGameMode('local')}
              >
                Local Mode
                <span className="mode-description">
                  Physical board and dice on table, devices handle money and cards
                </span>
              </button>
              <button 
                className="mode-button online"
                onClick={() => setGameMode('online')}
              >
                Online Mode
                <span className="mode-description">
                  Full digital experience with 2D board and virtual dice
                </span>
              </button>
            </div>
          </div>
        )}
        
        {gameMode === 'local' && (
          <div className="game-mode local-mode">
            <h2>Local Mode</h2>
            <p>Setting up local game...</p>
            <button onClick={() => setGameMode('lobby')}>Back to Lobby</button>
          </div>
        )}
        
        {gameMode === 'online' && (
          <div className="game-mode online-mode">
            <h2>Online Mode</h2>
            <p>Setting up online game...</p>
            <button onClick={() => setGameMode('lobby')}>Back to Lobby</button>
          </div>
        )}
      </main>
    </div>
  )
}

export default App
