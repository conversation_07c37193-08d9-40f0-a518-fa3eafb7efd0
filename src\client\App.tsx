import React, { useState } from 'react'
import GameClient from './components/GameClient'
import './App.css'

function App() {
  const [gameMode, setGameMode] = useState<'lobby' | 'local' | 'online'>('lobby')

  return (
    <div className="app">
      <header className="app-header">
        <h1>Daily Grind Dash</h1>
        <p>A browser-based multiplayer board game about financial literacy</p>
      </header>

      <main className="app-main">
        {gameMode === 'lobby' && (
          <div className="lobby">
            <h2>Welcome to Daily Grind Dash!</h2>
            <p>Choose your game mode:</p>
            <div className="mode-buttons">
              <button
                className="mode-button local"
                onClick={() => setGameMode('local')}
              >
                Local Mode
                <span className="mode-description">
                  Physical board and dice on table, devices handle money and cards
                </span>
              </button>
              <button
                className="mode-button online"
                onClick={() => setGameMode('online')}
              >
                Online Mode
                <span className="mode-description">
                  Full digital experience with 2D board and virtual dice
                </span>
              </button>
            </div>
          </div>
        )}

        {(gameMode === 'local' || gameMode === 'online') && (
          <GameClient
            gameMode={gameMode}
            onBackToLobby={() => setGameMode('lobby')}
          />
        )}
      </main>
    </div>
  )
}

export default App
