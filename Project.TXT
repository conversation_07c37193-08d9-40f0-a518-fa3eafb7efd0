Daily Grind Dash:

1 High-Level Vision
Everyone joins the same browser-based lobby (phone, tablet, or laptop). In Local Mode the physical board and dice stay on the table; each device only handles money, cards, offers, and approvals. In Online Mode a 2-D board, built with boardgame.io, renders pawns and rolls. The winner (for v1) is the first player whose passive income ≥ expenses. Up to ten players are supported per room.
boardgame.io

2 Core Gameplay Loop
Phase	Trigger	What the Device Does
Roll & Move	Local → players roll physical dice; Online → tap ROLL DICE (boardgame.io RNG)	If Online, pawn animates to tile; if Local, player taps Landed On and selects tile name from searchable list.
Resolve Tile	See tile list in §3	App privately shows any card; other players see a flashing badge beside that player’s avatar.
Deals & Offers	On Side Hustle / Grand Opportunity cards	Owner can Show to All, then tap Send Offer → choose Buy Out or Split Deal. Split UI displays 100 % pie; players enter % shares, see real-time totals, and submit. Counters force a fresh approval from all investors.
Income / Expense Tick	End of round	Device auto-adjusts cashflow, applies loan interest, credit-card payments.
Victory Check	End of each full turn	If passive ≥ expense: declare winner, archive session.

3 Tiles & Events (V1)
Board Tile	New Name / Function
Payday	Working Day – collect salary & pay automatic bills.
Charity	Give Back – may donate X % income to roll 3 dice next turn.
Market	Market – draw Market card (prices rise/fall).
Side Hustles	Side Hustles – draw small-deal card.
Grand Opportunities	Grand Opportunities – draw big-deal card.
Gizmos / Doohickeys	Gizmos – unexpected expenses.
Downsized	Job Shake-Up – miss two turns, pay fixed charges.
Divorced	Life Split – halve cash, adjust expenses.

Deck sizes are placeholder (10 Gizmos, 15 Side Hustles, 10 Grand Opps, 8 Market) to speed play-testing. Cards are dated to 2025 dollars so inflation math is static in v1.

4 Card, Offer & Chat Flows
Draw → card appears privately.

Badge Notify → all avatars show small glowing dot.

Show to All / Show to Some → pushes card image to chosen recipients.

Offer Panel

Buy Out: flat cash + optional commission.

Split Deal: slider or numeric % entry; total must hit 100 %.

Counter → owner edits splits, optionally pulls in more players; all prior investors must re-confirm.

Chats

Global: permanent log.

Deal-Private: auto-created thread tied to card ID.

Direct: tap player avatar → DM.

Credit & Loans (MVP)
Each profession starts with: salary, fixed expenses, a single credit-card limit, one loan slot. Interest rates are hard-coded averages for 2025 (≈ 22 % APR cards; ≈ 7 % real-estate loans).
marketwatch.com

5 Dice & Fairness
Online dice: ctx.random.D6() from boardgame.io; UUID seed stored in move log for transparency.
rocambille.github.io

Local mode: optional in-app roller for consistency; otherwise players key in roll value when selecting tile.

Charity triple-roll mechanic is kept (no evidence of patent coverage, only a board-specific rule).

6 Architecture & Tech Stack
6.1 Client (PWA)
React + TypeScript rendered with Vite; service worker enables offline caching and “Add to Home Screen.”
stackoverflow.com
create-react-app.dev

Storybook inside repo showcases cards, offers, and badge states.

6.2 Game Logic
boardgame.io handles state, moves, phases, and built-in RNG.
github.com

6.3 Networking & Back-End
Lightweight Node server created via boardgame.io/server; stateless WebSocket rooms.
github.com
github.com

Supabase Realtime for lobby auth + persistence when V2 adds resume-game feature (free tier covers 10 k monthly messages).
supabase.com
reddit.com

6.4 Persistence Roadmap
v1: session memory only (loss on refresh).

Schema already includes GameID, Turn, G, Players, Log. Save/Load API planned using Supabase Postgres plus localStorage fallback for disconnect resilience.
stackoverflow.com

6.5 Deployment
Static bundle → Netlify / Vercel drag-and-drop or GitHub Action CD.
github.com
github.com

Node WebSocket server can run as Netlify Function or Render free dyno.

7 Testing Assets
Prototype Asset	Purpose
6 Professions	Teacher, Engineer, Nurse, Sales Rep, Artist, Mechanic (each with salary, expenses, credit limit).
Mini Decks	3 Gizmos, 4 Side Hustles, 2 Grand Opportunities, 2 Market events.
Test Board	20-tile loop repeating the eight event spaces.

8 Future Extensions (V2)
Late-game replacement for “Fast Track” (placeholder).

Dynamic inflation feed (BLS CPI API).

Multi-card credit system with score drift.

Cloud save & resume mid-week.

Accessible font toggle and high-contrast mode.

Optional isometric board skin.

9 Repository & Documentation Structure
bash
Copy
Edit
/src
  /game        ← boardgame.io logic
  /client      ← React PWA
  /server      ← Node WS
/docs
  GDD.md       ← this file
  API.md       ← move / state contracts
/storybook     ← UI cases
Each merge runs CI: lint, unit tests, Storybook build, Netlify deploy.
medium.com