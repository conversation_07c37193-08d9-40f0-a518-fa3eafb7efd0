import React, { useState } from 'react';
import { Client } from 'boardgame.io/react';
import { SocketIO } from 'boardgame.io/multiplayer';
import { DailyGrindDash } from '../../game/game';
import { GameState, Player } from '../../game/types';
import { PROFESSIONS } from '../../game/constants';
import GameBoard from './GameBoard';
import CardComponent from './CardComponent';
import ChatComponent from './ChatComponent';
import './GameClient.css';

interface GameClientProps {
  gameMode: 'local' | 'online';
  onBackToLobby: () => void;
}

interface GameBoardProps {
  G: GameState;
  ctx: any;
  moves: any;
  playerID: string | null;
  isActive: boolean;
}

const GameBoardComponent: React.FC<GameBoardProps> = ({ G, ctx, moves, playerID, isActive }) => {
  const [selectedProfession, setSelectedProfession] = useState<string>('');
  const [playerName, setPlayerName] = useState<string>('');
  const [showJoinForm, setShowJoinForm] = useState<boolean>(!G.players[playerID]);
  const [selectedTileId, setSelectedTileId] = useState<number | null>(null);
  const [showCards, setShowCards] = useState<boolean>(false);

  const currentPlayer = G.players[playerID];
  const isCurrentTurn = G.currentPlayer === playerID;

  const handleJoinGame = () => {
    if (playerName && selectedProfession) {
      moves.joinGame(playerName, selectedProfession);
      setShowJoinForm(false);
    }
  };

  const handleRollDice = () => {
    if (isCurrentTurn && G.phase === 'roll') {
      moves.rollDice();
    }
  };

  const handleSelectTile = (tileId: number) => {
    if (isCurrentTurn && G.phase === 'roll' && G.gameMode === 'local') {
      moves.selectTile(tileId);
    }
  };

  const handleDrawCard = (deckType: string) => {
    if (isCurrentTurn && G.phase === 'resolve') {
      moves.drawCard(deckType);
    }
  };

  const handleEndTurn = () => {
    if (isCurrentTurn && G.phase === 'resolve') {
      moves.endTurn();
    }
  };

  const handleSendMessage = (type: 'global' | 'deal' | 'direct', message: string, recipientId?: string, dealId?: string) => {
    moves.sendMessage(type, message, recipientId, dealId);
  };

  if (showJoinForm) {
    return (
      <div className="join-form">
        <h2>Join the Game</h2>
        <div className="form-group">
          <label htmlFor="playerName">Your Name:</label>
          <input
            id="playerName"
            type="text"
            value={playerName}
            onChange={(e) => setPlayerName(e.target.value)}
            placeholder="Enter your name"
          />
        </div>
        <div className="form-group">
          <label htmlFor="profession">Choose Your Profession:</label>
          <select
            id="profession"
            value={selectedProfession}
            onChange={(e) => setSelectedProfession(e.target.value)}
          >
            <option value="">Select a profession</option>
            {PROFESSIONS.map((prof) => (
              <option key={prof.name} value={prof.name}>
                {prof.name} (Salary: ${prof.salary}/month, Expenses: ${prof.expenses}/month)
              </option>
            ))}
          </select>
        </div>
        <button 
          onClick={handleJoinGame}
          disabled={!playerName || !selectedProfession}
          className="join-button"
        >
          Join Game
        </button>
      </div>
    );
  }

  if (G.winner) {
    const winner = G.players[G.winner];
    return (
      <div className="victory-screen">
        <h1>🎉 Game Over! 🎉</h1>
        <h2>{winner.name} Wins!</h2>
        <p>
          {winner.name} achieved financial freedom with passive income of ${winner.passiveIncome}/month 
          exceeding expenses of ${winner.expenses}/month!
        </p>
        <div className="final-stats">
          <h3>Final Statistics:</h3>
          {Object.values(G.players).map((player) => (
            <div key={player.id} className={`player-final-stats ${player.id === G.winner ? 'winner' : ''}`}>
              <h4>{player.name} ({player.profession.name})</h4>
              <p>Cash: ${player.cash}</p>
              <p>Passive Income: ${player.passiveIncome}/month</p>
              <p>Expenses: ${player.expenses}/month</p>
              <p>Net Cashflow: ${player.passiveIncome - player.expenses}/month</p>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const getCurrentTileActions = () => {
    if (!currentPlayer || !isCurrentTurn || G.phase !== 'resolve') return null;

    const currentTile = G.board[currentPlayer.position];

    switch (currentTile.type) {
      case 'Market':
        return (
          <button
            onClick={() => handleDrawCard('market')}
            className="action-button draw-button"
          >
            📈 Draw Market Card
          </button>
        );
      case 'Side Hustles':
        return (
          <button
            onClick={() => handleDrawCard('sideHustle')}
            className="action-button draw-button"
          >
            💡 Draw Side Hustle Card
          </button>
        );
      case 'Grand Opportunities':
        return (
          <button
            onClick={() => handleDrawCard('grandOpportunity')}
            className="action-button draw-button"
          >
            🏆 Draw Grand Opportunity Card
          </button>
        );
      case 'Gizmos':
        return (
          <button
            onClick={() => handleDrawCard('gizmo')}
            className="action-button draw-button"
          >
            ⚙️ Draw Gizmo Card
          </button>
        );
      case 'Working Day':
        return (
          <div className="working-day-info">
            <p>💼 Payday! Collecting salary and paying bills...</p>
          </div>
        );
      case 'Give Back':
        return (
          <div className="give-back-info">
            <p>❤️ Consider donating to charity for bonus dice next turn</p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="game-board-wrapper">
      <div className="game-header">
        <h2>Daily Grind Dash</h2>
        <div className="game-info">
          <span>Round: {G.round}</span>
          <span>Turn: {G.turn}</span>
          <span>Phase: {G.phase}</span>
        </div>
        <div className="header-actions">
          <button
            className="toggle-cards-btn"
            onClick={() => setShowCards(!showCards)}
          >
            🃏 Cards ({currentPlayer?.cards.length || 0})
          </button>
        </div>
      </div>

      {G.gameMode === 'online' && (
        <GameBoard G={G} playerID={playerID} />
      )}

      <div className="game-content">
        <div className="player-info">
          <h3>Your Info</h3>
          {currentPlayer && (
            <div className="player-card">
              <h4>{currentPlayer.name}</h4>
              <p><strong>Profession:</strong> {currentPlayer.profession.name}</p>
              <p><strong>Position:</strong> {currentPlayer.position} - {G.board[currentPlayer.position].name}</p>
              <p><strong>Cash:</strong> ${currentPlayer.cash.toLocaleString()}</p>
              <p><strong>Passive Income:</strong> ${currentPlayer.passiveIncome}/month</p>
              <p><strong>Expenses:</strong> ${currentPlayer.expenses}/month</p>
              <p><strong>Net Cashflow:</strong> ${currentPlayer.passiveIncome - currentPlayer.expenses}/month</p>
              <div className="progress-to-victory">
                <p><strong>Victory Progress:</strong> {currentPlayer.passiveIncome >= currentPlayer.expenses ? '✅ ACHIEVED!' : `Need $${currentPlayer.expenses - currentPlayer.passiveIncome} more passive income`}</p>
              </div>
            </div>
          )}
        </div>

        <div className="board-section">
          <h3>Current Actions</h3>

          <div className="game-actions">
            {isCurrentTurn && G.phase === 'roll' && G.gameMode === 'online' && (
              <button onClick={handleRollDice} className="action-button roll-button">
                🎲 Roll Dice
              </button>
            )}

            {isCurrentTurn && G.phase === 'roll' && G.gameMode === 'local' && (
              <div className="local-mode-actions">
                <p>Roll physical dice and select your tile:</p>
                <select
                  value={selectedTileId || ''}
                  onChange={(e) => setSelectedTileId(Number(e.target.value))}
                >
                  <option value="">Select tile...</option>
                  {G.board.map((tile, index) => (
                    <option key={index} value={index}>
                      {index}: {tile.name}
                    </option>
                  ))}
                </select>
                <button
                  onClick={() => selectedTileId !== null && handleSelectTile(selectedTileId)}
                  disabled={selectedTileId === null}
                  className="action-button select-button"
                >
                  Land On Tile
                </button>
              </div>
            )}

            {getCurrentTileActions()}

            {isCurrentTurn && G.phase === 'resolve' && (
              <button onClick={handleEndTurn} className="action-button end-turn-button">
                End Turn
              </button>
            )}

            {!isCurrentTurn && (
              <p className="waiting-message">
                Waiting for {G.players[G.currentPlayer]?.name || 'other player'}...
              </p>
            )}
          </div>
        </div>

        <div className="other-players">
          <h3>Other Players</h3>
          {Object.values(G.players)
            .filter(player => player.id !== playerID)
            .map((player) => (
              <div key={player.id} className={`other-player ${player.id === G.currentPlayer ? 'current-turn' : ''}`}>
                <h4>{player.name} ({player.profession.name})</h4>
                <p>Position: {player.position} - {G.board[player.position].name}</p>
                <p>Cash: ${player.cash.toLocaleString()} | Passive: ${player.passiveIncome} | Expenses: ${player.expenses}</p>
              </div>
            ))}
        </div>
      </div>

      {showCards && currentPlayer && (
        <div className="cards-overlay">
          <div className="cards-modal">
            <div className="cards-header">
              <h3>Your Cards</h3>
              <button onClick={() => setShowCards(false)} className="close-btn">×</button>
            </div>
            <div className="cards-grid">
              {currentPlayer.cards.length === 0 ? (
                <p>No cards yet. Land on card tiles to draw cards!</p>
              ) : (
                currentPlayer.cards.map((card) => (
                  <CardComponent
                    key={card.id}
                    card={card}
                    isOwned={true}
                    canCreateDeal={true}
                    players={Object.fromEntries(
                      Object.entries(G.players).map(([id, player]) => [id, { id, name: player.name }])
                    )}
                    currentPlayerId={playerID}
                  />
                ))
              )}
            </div>
          </div>
        </div>
      )}

      <ChatComponent
        messages={G.chatMessages}
        players={Object.fromEntries(
          Object.entries(G.players).map(([id, player]) => [id, { id, name: player.name }])
        )}
        currentPlayerId={playerID}
        onSendMessage={handleSendMessage}
        activeDeals={G.activeDeals}
      />
    </div>
  );
};

const GameClient: React.FC<GameClientProps> = ({ gameMode, onBackToLobby }) => {
  const [gameID, setGameID] = useState<string>('');
  const [playerID, setPlayerID] = useState<string>('');
  const [showGameSetup, setShowGameSetup] = useState<boolean>(true);

  const handleCreateGame = () => {
    const newGameID = Math.random().toString(36).substring(2, 15);
    setGameID(newGameID);
    setPlayerID('0');
    setShowGameSetup(false);
  };

  const handleJoinGame = () => {
    if (gameID) {
      const newPlayerID = Math.random().toString(36).substring(2, 15);
      setPlayerID(newPlayerID);
      setShowGameSetup(false);
    }
  };

  if (showGameSetup) {
    return (
      <div className="game-setup">
        <h2>{gameMode === 'online' ? 'Online' : 'Local'} Game Setup</h2>
        
        <div className="setup-options">
          <div className="setup-option">
            <h3>Create New Game</h3>
            <button onClick={handleCreateGame} className="setup-button">
              Create Game
            </button>
          </div>
          
          <div className="setup-option">
            <h3>Join Existing Game</h3>
            <input
              type="text"
              placeholder="Enter Game ID"
              value={gameID}
              onChange={(e) => setGameID(e.target.value)}
            />
            <button 
              onClick={handleJoinGame} 
              disabled={!gameID}
              className="setup-button"
            >
              Join Game
            </button>
          </div>
        </div>
        
        <button onClick={onBackToLobby} className="back-button">
          Back to Lobby
        </button>
      </div>
    );
  }

  // Create the boardgame.io client
  const DailyGrindDashClient = Client({
    game: DailyGrindDash,
    board: GameBoardComponent,
    multiplayer: gameMode === 'online' ? SocketIO({ server: 'localhost:8001' }) : undefined,
  });

  return (
    <div className="game-client">
      <DailyGrindDashClient gameID={gameID} playerID={playerID} />
    </div>
  );
};

export default GameClient;
