import React, { useState, useRef, useEffect } from 'react';
import { ChatMessage } from '../../game/types';
import './ChatComponent.css';

interface ChatComponentProps {
  messages: ChatMessage[];
  players: { [id: string]: { id: string; name: string } };
  currentPlayerId: string;
  onSendMessage: (type: 'global' | 'deal' | 'direct', message: string, recipientId?: string, dealId?: string) => void;
  activeDeals?: { id: string; cardId: string }[];
}

const ChatComponent: React.FC<ChatComponentProps> = ({
  messages,
  players,
  currentPlayerId,
  onSendMessage,
  activeDeals = []
}) => {
  const [activeTab, setActiveTab] = useState<'global' | 'deals' | 'direct'>('global');
  const [selectedDeal, setSelectedDeal] = useState<string>('');
  const [selectedPlayer, setSelectedPlayer] = useState<string>('');
  const [messageInput, setMessageInput] = useState<string>('');
  const [isMinimized, setIsMinimized] = useState<boolean>(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const getFilteredMessages = () => {
    switch (activeTab) {
      case 'global':
        return messages.filter(msg => msg.type === 'global');
      case 'deals':
        return selectedDeal 
          ? messages.filter(msg => msg.type === 'deal' && msg.dealId === selectedDeal)
          : [];
      case 'direct':
        return selectedPlayer
          ? messages.filter(msg => 
              msg.type === 'direct' && 
              ((msg.senderId === currentPlayerId && msg.recipientId === selectedPlayer) ||
               (msg.senderId === selectedPlayer && msg.recipientId === currentPlayerId))
            )
          : [];
      default:
        return [];
    }
  };

  const handleSendMessage = () => {
    if (!messageInput.trim()) return;

    switch (activeTab) {
      case 'global':
        onSendMessage('global', messageInput);
        break;
      case 'deals':
        if (selectedDeal) {
          onSendMessage('deal', messageInput, undefined, selectedDeal);
        }
        break;
      case 'direct':
        if (selectedPlayer) {
          onSendMessage('direct', messageInput, selectedPlayer);
        }
        break;
    }
    setMessageInput('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getPlayerName = (playerId: string) => {
    return players[playerId]?.name || `Player ${playerId}`;
  };

  const filteredMessages = getFilteredMessages();

  if (isMinimized) {
    return (
      <div className="chat-minimized">
        <button 
          className="chat-toggle"
          onClick={() => setIsMinimized(false)}
        >
          💬 Chat ({messages.length})
        </button>
      </div>
    );
  }

  return (
    <div className="chat-component">
      <div className="chat-header">
        <h3>💬 Chat</h3>
        <button 
          className="minimize-btn"
          onClick={() => setIsMinimized(true)}
        >
          −
        </button>
      </div>

      <div className="chat-tabs">
        <button 
          className={`tab ${activeTab === 'global' ? 'active' : ''}`}
          onClick={() => setActiveTab('global')}
        >
          🌍 Global
        </button>
        <button 
          className={`tab ${activeTab === 'deals' ? 'active' : ''}`}
          onClick={() => setActiveTab('deals')}
        >
          🤝 Deals
        </button>
        <button 
          className={`tab ${activeTab === 'direct' ? 'active' : ''}`}
          onClick={() => setActiveTab('direct')}
        >
          💌 Direct
        </button>
      </div>

      {activeTab === 'deals' && (
        <div className="deal-selector">
          <select 
            value={selectedDeal}
            onChange={(e) => setSelectedDeal(e.target.value)}
          >
            <option value="">Select a deal...</option>
            {activeDeals.map(deal => (
              <option key={deal.id} value={deal.id}>
                Deal: {deal.cardId}
              </option>
            ))}
          </select>
        </div>
      )}

      {activeTab === 'direct' && (
        <div className="player-selector">
          <select 
            value={selectedPlayer}
            onChange={(e) => setSelectedPlayer(e.target.value)}
          >
            <option value="">Select a player...</option>
            {Object.entries(players)
              .filter(([id]) => id !== currentPlayerId)
              .map(([id, player]) => (
                <option key={id} value={id}>
                  {player.name}
                </option>
              ))}
          </select>
        </div>
      )}

      <div className="chat-messages">
        {filteredMessages.length === 0 ? (
          <div className="no-messages">
            {activeTab === 'global' && "No messages yet. Start the conversation!"}
            {activeTab === 'deals' && !selectedDeal && "Select a deal to view messages"}
            {activeTab === 'deals' && selectedDeal && "No messages for this deal yet"}
            {activeTab === 'direct' && !selectedPlayer && "Select a player to chat with"}
            {activeTab === 'direct' && selectedPlayer && "No messages yet. Say hello!"}
          </div>
        ) : (
          filteredMessages.map((message) => (
            <div 
              key={message.id}
              className={`message ${message.senderId === currentPlayerId ? 'own' : 'other'}`}
            >
              <div className="message-header">
                <span className="sender-name">
                  {getPlayerName(message.senderId)}
                </span>
                <span className="timestamp">
                  {formatTimestamp(message.timestamp)}
                </span>
              </div>
              <div className="message-content">
                {message.message}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      <div className="chat-input">
        <textarea
          value={messageInput}
          onChange={(e) => setMessageInput(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={
            activeTab === 'global' ? "Type a message to everyone..." :
            activeTab === 'deals' && !selectedDeal ? "Select a deal first..." :
            activeTab === 'direct' && !selectedPlayer ? "Select a player first..." :
            "Type a message..."
          }
          disabled={
            (activeTab === 'deals' && !selectedDeal) ||
            (activeTab === 'direct' && !selectedPlayer)
          }
          rows={2}
        />
        <button 
          className="send-btn"
          onClick={handleSendMessage}
          disabled={
            !messageInput.trim() ||
            (activeTab === 'deals' && !selectedDeal) ||
            (activeTab === 'direct' && !selectedPlayer)
          }
        >
          Send
        </button>
      </div>
    </div>
  );
};

export default ChatComponent;
