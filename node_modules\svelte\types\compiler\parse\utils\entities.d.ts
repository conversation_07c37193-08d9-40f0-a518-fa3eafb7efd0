declare const _default: {
    'CounterClockwiseContourIntegral;': number;
    'ClockwiseContourIntegral;': number;
    'DoubleLongLeftRightArrow;': number;
    'NotNestedGreaterGreater;': number;
    'DiacriticalDoubleAcute;': number;
    'NotSquareSupersetEqual;': number;
    'CloseCurlyDoubleQuote;': number;
    'DoubleContourIntegral;': number;
    'FilledVerySmallSquare;': number;
    'NegativeVeryThinSpace;': number;
    'NotPrecedesSlantEqual;': number;
    'NotRightTriangleEqual;': number;
    'NotSucceedsSlantEqual;': number;
    'CapitalDifferentialD;': number;
    'DoubleLeftRightArrow;': number;
    'DoubleLongRightArrow;': number;
    'EmptyVerySmallSquare;': number;
    'NestedGreaterGreater;': number;
    'NotDoubleVerticalBar;': number;
    'NotGreaterSlantEqual;': number;
    'NotLeftTriangleEqual;': number;
    'NotSquareSubsetEqual;': number;
    'OpenCurlyDoubleQuote;': number;
    'ReverseUpEquilibrium;': number;
    'DoubleLongLeftArrow;': number;
    'DownLeftRightVector;': number;
    'LeftArrowRightArrow;': number;
    'NegativeMediumSpace;': number;
    'NotGreaterFullEqual;': number;
    'NotRightTriangleBar;': number;
    'RightArrowLeftArrow;': number;
    'SquareSupersetEqual;': number;
    'leftrightsquigarrow;': number;
    'DownRightTeeVector;': number;
    'DownRightVectorBar;': number;
    'LongLeftRightArrow;': number;
    'Longleftrightarrow;': number;
    'NegativeThickSpace;': number;
    'NotLeftTriangleBar;': number;
    'PrecedesSlantEqual;': number;
    'ReverseEquilibrium;': number;
    'RightDoubleBracket;': number;
    'RightDownTeeVector;': number;
    'RightDownVectorBar;': number;
    'RightTriangleEqual;': number;
    'SquareIntersection;': number;
    'SucceedsSlantEqual;': number;
    'blacktriangleright;': number;
    'longleftrightarrow;': number;
    'DoubleUpDownArrow;': number;
    'DoubleVerticalBar;': number;
    'DownLeftTeeVector;': number;
    'DownLeftVectorBar;': number;
    'FilledSmallSquare;': number;
    'GreaterSlantEqual;': number;
    'LeftDoubleBracket;': number;
    'LeftDownTeeVector;': number;
    'LeftDownVectorBar;': number;
    'LeftTriangleEqual;': number;
    'NegativeThinSpace;': number;
    'NotGreaterGreater;': number;
    'NotLessSlantEqual;': number;
    'NotNestedLessLess;': number;
    'NotReverseElement;': number;
    'NotSquareSuperset;': number;
    'NotTildeFullEqual;': number;
    'RightAngleBracket;': number;
    'RightUpDownVector;': number;
    'SquareSubsetEqual;': number;
    'VerticalSeparator;': number;
    'blacktriangledown;': number;
    'blacktriangleleft;': number;
    'leftrightharpoons;': number;
    'rightleftharpoons;': number;
    'twoheadrightarrow;': number;
    'DiacriticalAcute;': number;
    'DiacriticalGrave;': number;
    'DiacriticalTilde;': number;
    'DoubleRightArrow;': number;
    'DownArrowUpArrow;': number;
    'EmptySmallSquare;': number;
    'GreaterEqualLess;': number;
    'GreaterFullEqual;': number;
    'LeftAngleBracket;': number;
    'LeftUpDownVector;': number;
    'LessEqualGreater;': number;
    'NonBreakingSpace;': number;
    'NotPrecedesEqual;': number;
    'NotRightTriangle;': number;
    'NotSucceedsEqual;': number;
    'NotSucceedsTilde;': number;
    'NotSupersetEqual;': number;
    'RightTriangleBar;': number;
    'RightUpTeeVector;': number;
    'RightUpVectorBar;': number;
    'UnderParenthesis;': number;
    'UpArrowDownArrow;': number;
    'circlearrowright;': number;
    'downharpoonright;': number;
    'ntrianglerighteq;': number;
    'rightharpoondown;': number;
    'rightrightarrows;': number;
    'twoheadleftarrow;': number;
    'vartriangleright;': number;
    'CloseCurlyQuote;': number;
    'ContourIntegral;': number;
    'DoubleDownArrow;': number;
    'DoubleLeftArrow;': number;
    'DownRightVector;': number;
    'LeftRightVector;': number;
    'LeftTriangleBar;': number;
    'LeftUpTeeVector;': number;
    'LeftUpVectorBar;': number;
    'LowerRightArrow;': number;
    'NotGreaterEqual;': number;
    'NotGreaterTilde;': number;
    'NotHumpDownHump;': number;
    'NotLeftTriangle;': number;
    'NotSquareSubset;': number;
    'OverParenthesis;': number;
    'RightDownVector;': number;
    'ShortRightArrow;': number;
    'UpperRightArrow;': number;
    'bigtriangledown;': number;
    'circlearrowleft;': number;
    'curvearrowright;': number;
    'downharpoonleft;': number;
    'leftharpoondown;': number;
    'leftrightarrows;': number;
    'nLeftrightarrow;': number;
    'nleftrightarrow;': number;
    'ntrianglelefteq;': number;
    'rightleftarrows;': number;
    'rightsquigarrow;': number;
    'rightthreetimes;': number;
    'straightepsilon;': number;
    'trianglerighteq;': number;
    'vartriangleleft;': number;
    'DiacriticalDot;': number;
    'DoubleRightTee;': number;
    'DownLeftVector;': number;
    'GreaterGreater;': number;
    'HorizontalLine;': number;
    'InvisibleComma;': number;
    'InvisibleTimes;': number;
    'LeftDownVector;': number;
    'LeftRightArrow;': number;
    'Leftrightarrow;': number;
    'LessSlantEqual;': number;
    'LongRightArrow;': number;
    'Longrightarrow;': number;
    'LowerLeftArrow;': number;
    'NestedLessLess;': number;
    'NotGreaterLess;': number;
    'NotLessGreater;': number;
    'NotSubsetEqual;': number;
    'NotVerticalBar;': number;
    'OpenCurlyQuote;': number;
    'ReverseElement;': number;
    'RightTeeVector;': number;
    'RightVectorBar;': number;
    'ShortDownArrow;': number;
    'ShortLeftArrow;': number;
    'SquareSuperset;': number;
    'TildeFullEqual;': number;
    'UpperLeftArrow;': number;
    'ZeroWidthSpace;': number;
    'curvearrowleft;': number;
    'doublebarwedge;': number;
    'downdownarrows;': number;
    'hookrightarrow;': number;
    'leftleftarrows;': number;
    'leftrightarrow;': number;
    'leftthreetimes;': number;
    'longrightarrow;': number;
    'looparrowright;': number;
    'nshortparallel;': number;
    'ntriangleright;': number;
    'rightarrowtail;': number;
    'rightharpoonup;': number;
    'trianglelefteq;': number;
    'upharpoonright;': number;
    'ApplyFunction;': number;
    'DifferentialD;': number;
    'DoubleLeftTee;': number;
    'DoubleUpArrow;': number;
    'LeftTeeVector;': number;
    'LeftVectorBar;': number;
    'LessFullEqual;': number;
    'LongLeftArrow;': number;
    'Longleftarrow;': number;
    'NotEqualTilde;': number;
    'NotTildeEqual;': number;
    'NotTildeTilde;': number;
    'Poincareplane;': number;
    'PrecedesEqual;': number;
    'PrecedesTilde;': number;
    'RightArrowBar;': number;
    'RightTeeArrow;': number;
    'RightTriangle;': number;
    'RightUpVector;': number;
    'SucceedsEqual;': number;
    'SucceedsTilde;': number;
    'SupersetEqual;': number;
    'UpEquilibrium;': number;
    'VerticalTilde;': number;
    'VeryThinSpace;': number;
    'bigtriangleup;': number;
    'blacktriangle;': number;
    'divideontimes;': number;
    'fallingdotseq;': number;
    'hookleftarrow;': number;
    'leftarrowtail;': number;
    'leftharpoonup;': number;
    'longleftarrow;': number;
    'looparrowleft;': number;
    'measuredangle;': number;
    'ntriangleleft;': number;
    'shortparallel;': number;
    'smallsetminus;': number;
    'triangleright;': number;
    'upharpoonleft;': number;
    'varsubsetneqq;': number;
    'varsupsetneqq;': number;
    'DownArrowBar;': number;
    'DownTeeArrow;': number;
    'ExponentialE;': number;
    'GreaterEqual;': number;
    'GreaterTilde;': number;
    'HilbertSpace;': number;
    'HumpDownHump;': number;
    'Intersection;': number;
    'LeftArrowBar;': number;
    'LeftTeeArrow;': number;
    'LeftTriangle;': number;
    'LeftUpVector;': number;
    'NotCongruent;': number;
    'NotHumpEqual;': number;
    'NotLessEqual;': number;
    'NotLessTilde;': number;
    'Proportional;': number;
    'RightCeiling;': number;
    'RoundImplies;': number;
    'ShortUpArrow;': number;
    'SquareSubset;': number;
    'UnderBracket;': number;
    'VerticalLine;': number;
    'blacklozenge;': number;
    'exponentiale;': number;
    'risingdotseq;': number;
    'triangledown;': number;
    'triangleleft;': number;
    'varsubsetneq;': number;
    'varsupsetneq;': number;
    'CircleMinus;': number;
    'CircleTimes;': number;
    'Equilibrium;': number;
    'GreaterLess;': number;
    'LeftCeiling;': number;
    'LessGreater;': number;
    'MediumSpace;': number;
    'NotLessLess;': number;
    'NotPrecedes;': number;
    'NotSucceeds;': number;
    'NotSuperset;': number;
    'OverBracket;': number;
    'RightVector;': number;
    'Rrightarrow;': number;
    'RuleDelayed;': number;
    'SmallCircle;': number;
    'SquareUnion;': number;
    'SubsetEqual;': number;
    'UpDownArrow;': number;
    'Updownarrow;': number;
    'VerticalBar;': number;
    'backepsilon;': number;
    'blacksquare;': number;
    'circledcirc;': number;
    'circleddash;': number;
    'curlyeqprec;': number;
    'curlyeqsucc;': number;
    'diamondsuit;': number;
    'eqslantless;': number;
    'expectation;': number;
    'nRightarrow;': number;
    'nrightarrow;': number;
    'preccurlyeq;': number;
    'precnapprox;': number;
    'quaternions;': number;
    'straightphi;': number;
    'succcurlyeq;': number;
    'succnapprox;': number;
    'thickapprox;': number;
    'updownarrow;': number;
    'Bernoullis;': number;
    'CirclePlus;': number;
    'EqualTilde;': number;
    'Fouriertrf;': number;
    'ImaginaryI;': number;
    'Laplacetrf;': number;
    'LeftVector;': number;
    'Lleftarrow;': number;
    'NotElement;': number;
    'NotGreater;': number;
    'Proportion;': number;
    'RightArrow;': number;
    'RightFloor;': number;
    'Rightarrow;': number;
    'ThickSpace;': number;
    'TildeEqual;': number;
    'TildeTilde;': number;
    'UnderBrace;': number;
    'UpArrowBar;': number;
    'UpTeeArrow;': number;
    'circledast;': number;
    'complement;': number;
    'curlywedge;': number;
    'eqslantgtr;': number;
    'gtreqqless;': number;
    'lessapprox;': number;
    'lesseqqgtr;': number;
    'lmoustache;': number;
    'longmapsto;': number;
    'mapstodown;': number;
    'mapstoleft;': number;
    'nLeftarrow;': number;
    'nleftarrow;': number;
    'nsubseteqq;': number;
    'nsupseteqq;': number;
    'precapprox;': number;
    'rightarrow;': number;
    'rmoustache;': number;
    'sqsubseteq;': number;
    'sqsupseteq;': number;
    'subsetneqq;': number;
    'succapprox;': number;
    'supsetneqq;': number;
    'upuparrows;': number;
    'varepsilon;': number;
    'varnothing;': number;
    'Backslash;': number;
    'CenterDot;': number;
    'CircleDot;': number;
    'Congruent;': number;
    'Coproduct;': number;
    'DoubleDot;': number;
    'DownArrow;': number;
    'DownBreve;': number;
    'Downarrow;': number;
    'HumpEqual;': number;
    'LeftArrow;': number;
    'LeftFloor;': number;
    'Leftarrow;': number;
    'LessTilde;': number;
    'Mellintrf;': number;
    'MinusPlus;': number;
    'NotCupCap;': number;
    'NotExists;': number;
    'NotSubset;': number;
    'OverBrace;': number;
    'PlusMinus;': number;
    'Therefore;': number;
    'ThinSpace;': number;
    'TripleDot;': number;
    'UnionPlus;': number;
    'backprime;': number;
    'backsimeq;': number;
    'bigotimes;': number;
    'centerdot;': number;
    'checkmark;': number;
    'complexes;': number;
    'dotsquare;': number;
    'downarrow;': number;
    'gtrapprox;': number;
    'gtreqless;': number;
    'gvertneqq;': number;
    'heartsuit;': number;
    'leftarrow;': number;
    'lesseqgtr;': number;
    'lvertneqq;': number;
    'ngeqslant;': number;
    'nleqslant;': number;
    'nparallel;': number;
    'nshortmid;': number;
    'nsubseteq;': number;
    'nsupseteq;': number;
    'pitchfork;': number;
    'rationals;': number;
    'spadesuit;': number;
    'subseteqq;': number;
    'subsetneq;': number;
    'supseteqq;': number;
    'supsetneq;': number;
    'therefore;': number;
    'triangleq;': number;
    'varpropto;': number;
    'DDotrahd;': number;
    'DotEqual;': number;
    'Integral;': number;
    'LessLess;': number;
    'NotEqual;': number;
    'NotTilde;': number;
    'PartialD;': number;
    'Precedes;': number;
    'RightTee;': number;
    'Succeeds;': number;
    'SuchThat;': number;
    'Superset;': number;
    'Uarrocir;': number;
    'UnderBar;': number;
    'andslope;': number;
    'angmsdaa;': number;
    'angmsdab;': number;
    'angmsdac;': number;
    'angmsdad;': number;
    'angmsdae;': number;
    'angmsdaf;': number;
    'angmsdag;': number;
    'angmsdah;': number;
    'angrtvbd;': number;
    'approxeq;': number;
    'awconint;': number;
    'backcong;': number;
    'barwedge;': number;
    'bbrktbrk;': number;
    'bigoplus;': number;
    'bigsqcup;': number;
    'biguplus;': number;
    'bigwedge;': number;
    'boxminus;': number;
    'boxtimes;': number;
    'bsolhsub;': number;
    'capbrcup;': number;
    'circledR;': number;
    'circledS;': number;
    'cirfnint;': number;
    'clubsuit;': number;
    'cupbrcap;': number;
    'curlyvee;': number;
    'cwconint;': number;
    'doteqdot;': number;
    'dotminus;': number;
    'drbkarow;': number;
    'dzigrarr;': number;
    'elinters;': number;
    'emptyset;': number;
    'eqvparsl;': number;
    'fpartint;': number;
    'geqslant;': number;
    'gesdotol;': number;
    'gnapprox;': number;
    'hksearow;': number;
    'hkswarow;': number;
    'imagline;': number;
    'imagpart;': number;
    'infintie;': number;
    'integers;': number;
    'intercal;': number;
    'intlarhk;': number;
    'laemptyv;': number;
    'ldrushar;': number;
    'leqslant;': number;
    'lesdotor;': number;
    'llcorner;': number;
    'lnapprox;': number;
    'lrcorner;': number;
    'lurdshar;': number;
    'mapstoup;': number;
    'multimap;': number;
    'naturals;': number;
    'ncongdot;': number;
    'notindot;': number;
    'otimesas;': number;
    'parallel;': number;
    'plusacir;': number;
    'pointint;': number;
    'precneqq;': number;
    'precnsim;': number;
    'profalar;': number;
    'profline;': number;
    'profsurf;': number;
    'raemptyv;': number;
    'realpart;': number;
    'rppolint;': number;
    'rtriltri;': number;
    'scpolint;': number;
    'setminus;': number;
    'shortmid;': number;
    'smeparsl;': number;
    'sqsubset;': number;
    'sqsupset;': number;
    'subseteq;': number;
    'succneqq;': number;
    'succnsim;': number;
    'supseteq;': number;
    'thetasym;': number;
    'thicksim;': number;
    'timesbar;': number;
    'triangle;': number;
    'triminus;': number;
    'trpezium;': number;
    'ulcorner;': number;
    'urcorner;': number;
    'varkappa;': number;
    'varsigma;': number;
    'vartheta;': number;
    'Because;': number;
    'Cayleys;': number;
    'Cconint;': number;
    'Cedilla;': number;
    'Diamond;': number;
    'DownTee;': number;
    'Element;': number;
    'Epsilon;': number;
    'Implies;': number;
    'LeftTee;': number;
    'NewLine;': number;
    'NoBreak;': number;
    'NotLess;': number;
    'Omicron;': number;
    'OverBar;': number;
    'Product;': number;
    'UpArrow;': number;
    'Uparrow;': number;
    'Upsilon;': number;
    'alefsym;': number;
    'angrtvb;': number;
    'angzarr;': number;
    'asympeq;': number;
    'backsim;': number;
    'because;': number;
    'bemptyv;': number;
    'between;': number;
    'bigcirc;': number;
    'bigodot;': number;
    'bigstar;': number;
    'bnequiv;': number;
    'boxplus;': number;
    'ccupssm;': number;
    'cemptyv;': number;
    'cirscir;': number;
    'coloneq;': number;
    'congdot;': number;
    'cudarrl;': number;
    'cudarrr;': number;
    'cularrp;': number;
    'curarrm;': number;
    'dbkarow;': number;
    'ddagger;': number;
    'ddotseq;': number;
    'demptyv;': number;
    'diamond;': number;
    'digamma;': number;
    'dotplus;': number;
    'dwangle;': number;
    'epsilon;': number;
    'eqcolon;': number;
    'equivDD;': number;
    'gesdoto;': number;
    'gtquest;': number;
    'gtrless;': number;
    'harrcir;': number;
    'intprod;': number;
    'isindot;': number;
    'larrbfs;': number;
    'larrsim;': number;
    'lbrksld;': number;
    'lbrkslu;': number;
    'ldrdhar;': number;
    'lesdoto;': number;
    'lessdot;': number;
    'lessgtr;': number;
    'lesssim;': number;
    'lotimes;': number;
    'lozenge;': number;
    'ltquest;': number;
    'luruhar;': number;
    'maltese;': number;
    'minusdu;': number;
    'napprox;': number;
    'natural;': number;
    'nearrow;': number;
    'nexists;': number;
    'notinva;': number;
    'notinvb;': number;
    'notinvc;': number;
    'notniva;': number;
    'notnivb;': number;
    'notnivc;': number;
    'npolint;': number;
    'npreceq;': number;
    'nsqsube;': number;
    'nsqsupe;': number;
    'nsubset;': number;
    'nsucceq;': number;
    'nsupset;': number;
    'nvinfin;': number;
    'nvltrie;': number;
    'nvrtrie;': number;
    'nwarrow;': number;
    'olcross;': number;
    'omicron;': number;
    'orderof;': number;
    'orslope;': number;
    'pertenk;': number;
    'planckh;': number;
    'pluscir;': number;
    'plussim;': number;
    'plustwo;': number;
    'precsim;': number;
    'quatint;': number;
    'questeq;': number;
    'rarrbfs;': number;
    'rarrsim;': number;
    'rbrksld;': number;
    'rbrkslu;': number;
    'rdldhar;': number;
    'realine;': number;
    'rotimes;': number;
    'ruluhar;': number;
    'searrow;': number;
    'simplus;': number;
    'simrarr;': number;
    'subedot;': number;
    'submult;': number;
    'subplus;': number;
    'subrarr;': number;
    'succsim;': number;
    'supdsub;': number;
    'supedot;': number;
    'suphsol;': number;
    'suphsub;': number;
    'suplarr;': number;
    'supmult;': number;
    'supplus;': number;
    'swarrow;': number;
    'topfork;': number;
    'triplus;': number;
    'tritime;': number;
    'uparrow;': number;
    'upsilon;': number;
    'uwangle;': number;
    'vzigzag;': number;
    'zigrarr;': number;
    'Aacute;': number;
    'Abreve;': number;
    'Agrave;': number;
    'Assign;': number;
    'Atilde;': number;
    'Barwed;': number;
    'Bumpeq;': number;
    'Cacute;': number;
    'Ccaron;': number;
    'Ccedil;': number;
    'Colone;': number;
    'Conint;': number;
    'CupCap;': number;
    'Dagger;': number;
    'Dcaron;': number;
    'DotDot;': number;
    'Dstrok;': number;
    'Eacute;': number;
    'Ecaron;': number;
    'Egrave;': number;
    'Exists;': number;
    'ForAll;': number;
    'Gammad;': number;
    'Gbreve;': number;
    'Gcedil;': number;
    'HARDcy;': number;
    'Hstrok;': number;
    'Iacute;': number;
    'Igrave;': number;
    'Itilde;': number;
    'Jsercy;': number;
    'Kcedil;': number;
    'Lacute;': number;
    'Lambda;': number;
    'Lcaron;': number;
    'Lcedil;': number;
    'Lmidot;': number;
    'Lstrok;': number;
    'Nacute;': number;
    'Ncaron;': number;
    'Ncedil;': number;
    'Ntilde;': number;
    'Oacute;': number;
    'Odblac;': number;
    'Ograve;': number;
    'Oslash;': number;
    'Otilde;': number;
    'Otimes;': number;
    'Racute;': number;
    'Rarrtl;': number;
    'Rcaron;': number;
    'Rcedil;': number;
    'SHCHcy;': number;
    'SOFTcy;': number;
    'Sacute;': number;
    'Scaron;': number;
    'Scedil;': number;
    'Square;': number;
    'Subset;': number;
    'Supset;': number;
    'Tcaron;': number;
    'Tcedil;': number;
    'Tstrok;': number;
    'Uacute;': number;
    'Ubreve;': number;
    'Udblac;': number;
    'Ugrave;': number;
    'Utilde;': number;
    'Vdashl;': number;
    'Verbar;': number;
    'Vvdash;': number;
    'Yacute;': number;
    'Zacute;': number;
    'Zcaron;': number;
    'aacute;': number;
    'abreve;': number;
    'agrave;': number;
    'andand;': number;
    'angmsd;': number;
    'angsph;': number;
    'apacir;': number;
    'approx;': number;
    'atilde;': number;
    'barvee;': number;
    'barwed;': number;
    'becaus;': number;
    'bernou;': number;
    'bigcap;': number;
    'bigcup;': number;
    'bigvee;': number;
    'bkarow;': number;
    'bottom;': number;
    'bowtie;': number;
    'boxbox;': number;
    'bprime;': number;
    'brvbar;': number;
    'bullet;': number;
    'bumpeq;': number;
    'cacute;': number;
    'capand;': number;
    'capcap;': number;
    'capcup;': number;
    'capdot;': number;
    'ccaron;': number;
    'ccedil;': number;
    'circeq;': number;
    'cirmid;': number;
    'colone;': number;
    'commat;': number;
    'compfn;': number;
    'conint;': number;
    'coprod;': number;
    'copysr;': number;
    'cularr;': number;
    'cupcap;': number;
    'cupcup;': number;
    'cupdot;': number;
    'curarr;': number;
    'curren;': number;
    'cylcty;': number;
    'dagger;': number;
    'daleth;': number;
    'dcaron;': number;
    'dfisht;': number;
    'divide;': number;
    'divonx;': number;
    'dlcorn;': number;
    'dlcrop;': number;
    'dollar;': number;
    'drcorn;': number;
    'drcrop;': number;
    'dstrok;': number;
    'eacute;': number;
    'easter;': number;
    'ecaron;': number;
    'ecolon;': number;
    'egrave;': number;
    'egsdot;': number;
    'elsdot;': number;
    'emptyv;': number;
    'emsp13;': number;
    'emsp14;': number;
    'eparsl;': number;
    'eqcirc;': number;
    'equals;': number;
    'equest;': number;
    'female;': number;
    'ffilig;': number;
    'ffllig;': number;
    'forall;': number;
    'frac12;': number;
    'frac13;': number;
    'frac14;': number;
    'frac15;': number;
    'frac16;': number;
    'frac18;': number;
    'frac23;': number;
    'frac25;': number;
    'frac34;': number;
    'frac35;': number;
    'frac38;': number;
    'frac45;': number;
    'frac56;': number;
    'frac58;': number;
    'frac78;': number;
    'gacute;': number;
    'gammad;': number;
    'gbreve;': number;
    'gesdot;': number;
    'gesles;': number;
    'gtlPar;': number;
    'gtrarr;': number;
    'gtrdot;': number;
    'gtrsim;': number;
    'hairsp;': number;
    'hamilt;': number;
    'hardcy;': number;
    'hearts;': number;
    'hellip;': number;
    'hercon;': number;
    'homtht;': number;
    'horbar;': number;
    'hslash;': number;
    'hstrok;': number;
    'hybull;': number;
    'hyphen;': number;
    'iacute;': number;
    'igrave;': number;
    'iiiint;': number;
    'iinfin;': number;
    'incare;': number;
    'inodot;': number;
    'intcal;': number;
    'iquest;': number;
    'isinsv;': number;
    'itilde;': number;
    'jsercy;': number;
    'kappav;': number;
    'kcedil;': number;
    'kgreen;': number;
    'lAtail;': number;
    'lacute;': number;
    'lagran;': number;
    'lambda;': number;
    'langle;': number;
    'larrfs;': number;
    'larrhk;': number;
    'larrlp;': number;
    'larrpl;': number;
    'larrtl;': number;
    'latail;': number;
    'lbrace;': number;
    'lbrack;': number;
    'lcaron;': number;
    'lcedil;': number;
    'ldquor;': number;
    'lesdot;': number;
    'lesges;': number;
    'lfisht;': number;
    'lfloor;': number;
    'lharul;': number;
    'llhard;': number;
    'lmidot;': number;
    'lmoust;': number;
    'loplus;': number;
    'lowast;': number;
    'lowbar;': number;
    'lparlt;': number;
    'lrhard;': number;
    'lsaquo;': number;
    'lsquor;': number;
    'lstrok;': number;
    'lthree;': number;
    'ltimes;': number;
    'ltlarr;': number;
    'ltrPar;': number;
    'mapsto;': number;
    'marker;': number;
    'mcomma;': number;
    'midast;': number;
    'midcir;': number;
    'middot;': number;
    'minusb;': number;
    'minusd;': number;
    'mnplus;': number;
    'models;': number;
    'mstpos;': number;
    'nVDash;': number;
    'nVdash;': number;
    'nacute;': number;
    'nbumpe;': number;
    'ncaron;': number;
    'ncedil;': number;
    'nearhk;': number;
    'nequiv;': number;
    'nesear;': number;
    'nexist;': number;
    'nltrie;': number;
    'notinE;': number;
    'nparsl;': number;
    'nprcue;': number;
    'nrarrc;': number;
    'nrarrw;': number;
    'nrtrie;': number;
    'nsccue;': number;
    'nsimeq;': number;
    'ntilde;': number;
    'numero;': number;
    'nvDash;': number;
    'nvHarr;': number;
    'nvdash;': number;
    'nvlArr;': number;
    'nvrArr;': number;
    'nwarhk;': number;
    'nwnear;': number;
    'oacute;': number;
    'odblac;': number;
    'odsold;': number;
    'ograve;': number;
    'ominus;': number;
    'origof;': number;
    'oslash;': number;
    'otilde;': number;
    'otimes;': number;
    'parsim;': number;
    'percnt;': number;
    'period;': number;
    'permil;': number;
    'phmmat;': number;
    'planck;': number;
    'plankv;': number;
    'plusdo;': number;
    'plusdu;': number;
    'plusmn;': number;
    'preceq;': number;
    'primes;': number;
    'prnsim;': number;
    'propto;': number;
    'prurel;': number;
    'puncsp;': number;
    'qprime;': number;
    'rAtail;': number;
    'racute;': number;
    'rangle;': number;
    'rarrap;': number;
    'rarrfs;': number;
    'rarrhk;': number;
    'rarrlp;': number;
    'rarrpl;': number;
    'rarrtl;': number;
    'ratail;': number;
    'rbrace;': number;
    'rbrack;': number;
    'rcaron;': number;
    'rcedil;': number;
    'rdquor;': number;
    'rfisht;': number;
    'rfloor;': number;
    'rharul;': number;
    'rmoust;': number;
    'roplus;': number;
    'rpargt;': number;
    'rsaquo;': number;
    'rsquor;': number;
    'rthree;': number;
    'rtimes;': number;
    'sacute;': number;
    'scaron;': number;
    'scedil;': number;
    'scnsim;': number;
    'searhk;': number;
    'seswar;': number;
    'sfrown;': number;
    'shchcy;': number;
    'sigmaf;': number;
    'sigmav;': number;
    'simdot;': number;
    'smashp;': number;
    'softcy;': number;
    'solbar;': number;
    'spades;': number;
    'sqcaps;': number;
    'sqcups;': number;
    'sqsube;': number;
    'sqsupe;': number;
    'square;': number;
    'squarf;': number;
    'ssetmn;': number;
    'ssmile;': number;
    'sstarf;': number;
    'subdot;': number;
    'subset;': number;
    'subsim;': number;
    'subsub;': number;
    'subsup;': number;
    'succeq;': number;
    'supdot;': number;
    'supset;': number;
    'supsim;': number;
    'supsub;': number;
    'supsup;': number;
    'swarhk;': number;
    'swnwar;': number;
    'target;': number;
    'tcaron;': number;
    'tcedil;': number;
    'telrec;': number;
    'there4;': number;
    'thetav;': number;
    'thinsp;': number;
    'thksim;': number;
    'timesb;': number;
    'timesd;': number;
    'topbot;': number;
    'topcir;': number;
    'tprime;': number;
    'tridot;': number;
    'tstrok;': number;
    'uacute;': number;
    'ubreve;': number;
    'udblac;': number;
    'ufisht;': number;
    'ugrave;': number;
    'ulcorn;': number;
    'ulcrop;': number;
    'urcorn;': number;
    'urcrop;': number;
    'utilde;': number;
    'vangrt;': number;
    'varphi;': number;
    'varrho;': number;
    'veebar;': number;
    'vellip;': number;
    'verbar;': number;
    'vsubnE;': number;
    'vsubne;': number;
    'vsupnE;': number;
    'vsupne;': number;
    'wedbar;': number;
    'wedgeq;': number;
    'weierp;': number;
    'wreath;': number;
    'xoplus;': number;
    'xotime;': number;
    'xsqcup;': number;
    'xuplus;': number;
    'xwedge;': number;
    'yacute;': number;
    'zacute;': number;
    'zcaron;': number;
    'zeetrf;': number;
    'AElig;': number;
    Aacute: number;
    'Acirc;': number;
    Agrave: number;
    'Alpha;': number;
    'Amacr;': number;
    'Aogon;': number;
    'Aring;': number;
    Atilde: number;
    'Breve;': number;
    Ccedil: number;
    'Ccirc;': number;
    'Colon;': number;
    'Cross;': number;
    'Dashv;': number;
    'Delta;': number;
    Eacute: number;
    'Ecirc;': number;
    Egrave: number;
    'Emacr;': number;
    'Eogon;': number;
    'Equal;': number;
    'Gamma;': number;
    'Gcirc;': number;
    'Hacek;': number;
    'Hcirc;': number;
    'IJlig;': number;
    Iacute: number;
    'Icirc;': number;
    Igrave: number;
    'Imacr;': number;
    'Iogon;': number;
    'Iukcy;': number;
    'Jcirc;': number;
    'Jukcy;': number;
    'Kappa;': number;
    Ntilde: number;
    'OElig;': number;
    Oacute: number;
    'Ocirc;': number;
    Ograve: number;
    'Omacr;': number;
    'Omega;': number;
    Oslash: number;
    Otilde: number;
    'Prime;': number;
    'RBarr;': number;
    'Scirc;': number;
    'Sigma;': number;
    'THORN;': number;
    'TRADE;': number;
    'TSHcy;': number;
    'Theta;': number;
    'Tilde;': number;
    Uacute: number;
    'Ubrcy;': number;
    'Ucirc;': number;
    Ugrave: number;
    'Umacr;': number;
    'Union;': number;
    'Uogon;': number;
    'UpTee;': number;
    'Uring;': number;
    'VDash;': number;
    'Vdash;': number;
    'Wcirc;': number;
    'Wedge;': number;
    Yacute: number;
    'Ycirc;': number;
    aacute: number;
    'acirc;': number;
    'acute;': number;
    'aelig;': number;
    agrave: number;
    'aleph;': number;
    'alpha;': number;
    'amacr;': number;
    'amalg;': number;
    'angle;': number;
    'angrt;': number;
    'angst;': number;
    'aogon;': number;
    'aring;': number;
    'asymp;': number;
    atilde: number;
    'awint;': number;
    'bcong;': number;
    'bdquo;': number;
    'bepsi;': number;
    'blank;': number;
    'blk12;': number;
    'blk14;': number;
    'blk34;': number;
    'block;': number;
    'boxDL;': number;
    'boxDR;': number;
    'boxDl;': number;
    'boxDr;': number;
    'boxHD;': number;
    'boxHU;': number;
    'boxHd;': number;
    'boxHu;': number;
    'boxUL;': number;
    'boxUR;': number;
    'boxUl;': number;
    'boxUr;': number;
    'boxVH;': number;
    'boxVL;': number;
    'boxVR;': number;
    'boxVh;': number;
    'boxVl;': number;
    'boxVr;': number;
    'boxdL;': number;
    'boxdR;': number;
    'boxdl;': number;
    'boxdr;': number;
    'boxhD;': number;
    'boxhU;': number;
    'boxhd;': number;
    'boxhu;': number;
    'boxuL;': number;
    'boxuR;': number;
    'boxul;': number;
    'boxur;': number;
    'boxvH;': number;
    'boxvL;': number;
    'boxvR;': number;
    'boxvh;': number;
    'boxvl;': number;
    'boxvr;': number;
    'breve;': number;
    brvbar: number;
    'bsemi;': number;
    'bsime;': number;
    'bsolb;': number;
    'bumpE;': number;
    'bumpe;': number;
    'caret;': number;
    'caron;': number;
    'ccaps;': number;
    ccedil: number;
    'ccirc;': number;
    'ccups;': number;
    'cedil;': number;
    'check;': number;
    'clubs;': number;
    'colon;': number;
    'comma;': number;
    'crarr;': number;
    'cross;': number;
    'csube;': number;
    'csupe;': number;
    'ctdot;': number;
    'cuepr;': number;
    'cuesc;': number;
    'cupor;': number;
    curren: number;
    'cuvee;': number;
    'cuwed;': number;
    'cwint;': number;
    'dashv;': number;
    'dblac;': number;
    'ddarr;': number;
    'delta;': number;
    'dharl;': number;
    'dharr;': number;
    'diams;': number;
    'disin;': number;
    divide: number;
    'doteq;': number;
    'dtdot;': number;
    'dtrif;': number;
    'duarr;': number;
    'duhar;': number;
    'eDDot;': number;
    eacute: number;
    'ecirc;': number;
    'efDot;': number;
    egrave: number;
    'emacr;': number;
    'empty;': number;
    'eogon;': number;
    'eplus;': number;
    'epsiv;': number;
    'eqsim;': number;
    'equiv;': number;
    'erDot;': number;
    'erarr;': number;
    'esdot;': number;
    'exist;': number;
    'fflig;': number;
    'filig;': number;
    'fjlig;': number;
    'fllig;': number;
    'fltns;': number;
    'forkv;': number;
    frac12: number;
    frac14: number;
    frac34: number;
    'frasl;': number;
    'frown;': number;
    'gamma;': number;
    'gcirc;': number;
    'gescc;': number;
    'gimel;': number;
    'gneqq;': number;
    'gnsim;': number;
    'grave;': number;
    'gsime;': number;
    'gsiml;': number;
    'gtcir;': number;
    'gtdot;': number;
    'harrw;': number;
    'hcirc;': number;
    'hoarr;': number;
    iacute: number;
    'icirc;': number;
    'iexcl;': number;
    igrave: number;
    'iiint;': number;
    'iiota;': number;
    'ijlig;': number;
    'imacr;': number;
    'image;': number;
    'imath;': number;
    'imped;': number;
    'infin;': number;
    'iogon;': number;
    'iprod;': number;
    iquest: number;
    'isinE;': number;
    'isins;': number;
    'isinv;': number;
    'iukcy;': number;
    'jcirc;': number;
    'jmath;': number;
    'jukcy;': number;
    'kappa;': number;
    'lAarr;': number;
    'lBarr;': number;
    'langd;': number;
    'laquo;': number;
    'larrb;': number;
    'lates;': number;
    'lbarr;': number;
    'lbbrk;': number;
    'lbrke;': number;
    'lceil;': number;
    'ldquo;': number;
    'lescc;': number;
    'lhard;': number;
    'lharu;': number;
    'lhblk;': number;
    'llarr;': number;
    'lltri;': number;
    'lneqq;': number;
    'lnsim;': number;
    'loang;': number;
    'loarr;': number;
    'lobrk;': number;
    'lopar;': number;
    'lrarr;': number;
    'lrhar;': number;
    'lrtri;': number;
    'lsime;': number;
    'lsimg;': number;
    'lsquo;': number;
    'ltcir;': number;
    'ltdot;': number;
    'ltrie;': number;
    'ltrif;': number;
    'mDDot;': number;
    'mdash;': number;
    'micro;': number;
    middot: number;
    'minus;': number;
    'mumap;': number;
    'nabla;': number;
    'napid;': number;
    'napos;': number;
    'natur;': number;
    'nbump;': number;
    'ncong;': number;
    'ndash;': number;
    'neArr;': number;
    'nearr;': number;
    'nedot;': number;
    'nesim;': number;
    'ngeqq;': number;
    'ngsim;': number;
    'nhArr;': number;
    'nharr;': number;
    'nhpar;': number;
    'nlArr;': number;
    'nlarr;': number;
    'nleqq;': number;
    'nless;': number;
    'nlsim;': number;
    'nltri;': number;
    'notin;': number;
    'notni;': number;
    'npart;': number;
    'nprec;': number;
    'nrArr;': number;
    'nrarr;': number;
    'nrtri;': number;
    'nsime;': number;
    'nsmid;': number;
    'nspar;': number;
    'nsubE;': number;
    'nsube;': number;
    'nsucc;': number;
    'nsupE;': number;
    'nsupe;': number;
    ntilde: number;
    'numsp;': number;
    'nvsim;': number;
    'nwArr;': number;
    'nwarr;': number;
    oacute: number;
    'ocirc;': number;
    'odash;': number;
    'oelig;': number;
    'ofcir;': number;
    ograve: number;
    'ohbar;': number;
    'olarr;': number;
    'olcir;': number;
    'oline;': number;
    'omacr;': number;
    'omega;': number;
    'operp;': number;
    'oplus;': number;
    'orarr;': number;
    'order;': number;
    oslash: number;
    otilde: number;
    'ovbar;': number;
    'parsl;': number;
    'phone;': number;
    'plusb;': number;
    'pluse;': number;
    plusmn: number;
    'pound;': number;
    'prcue;': number;
    'prime;': number;
    'prnap;': number;
    'prsim;': number;
    'quest;': number;
    'rAarr;': number;
    'rBarr;': number;
    'radic;': number;
    'rangd;': number;
    'range;': number;
    'raquo;': number;
    'rarrb;': number;
    'rarrc;': number;
    'rarrw;': number;
    'ratio;': number;
    'rbarr;': number;
    'rbbrk;': number;
    'rbrke;': number;
    'rceil;': number;
    'rdquo;': number;
    'reals;': number;
    'rhard;': number;
    'rharu;': number;
    'rlarr;': number;
    'rlhar;': number;
    'rnmid;': number;
    'roang;': number;
    'roarr;': number;
    'robrk;': number;
    'ropar;': number;
    'rrarr;': number;
    'rsquo;': number;
    'rtrie;': number;
    'rtrif;': number;
    'sbquo;': number;
    'sccue;': number;
    'scirc;': number;
    'scnap;': number;
    'scsim;': number;
    'sdotb;': number;
    'sdote;': number;
    'seArr;': number;
    'searr;': number;
    'setmn;': number;
    'sharp;': number;
    'sigma;': number;
    'simeq;': number;
    'simgE;': number;
    'simlE;': number;
    'simne;': number;
    'slarr;': number;
    'smile;': number;
    'smtes;': number;
    'sqcap;': number;
    'sqcup;': number;
    'sqsub;': number;
    'sqsup;': number;
    'srarr;': number;
    'starf;': number;
    'strns;': number;
    'subnE;': number;
    'subne;': number;
    'supnE;': number;
    'supne;': number;
    'swArr;': number;
    'swarr;': number;
    'szlig;': number;
    'theta;': number;
    'thkap;': number;
    'thorn;': number;
    'tilde;': number;
    'times;': number;
    'trade;': number;
    'trisb;': number;
    'tshcy;': number;
    'twixt;': number;
    uacute: number;
    'ubrcy;': number;
    'ucirc;': number;
    'udarr;': number;
    'udhar;': number;
    ugrave: number;
    'uharl;': number;
    'uharr;': number;
    'uhblk;': number;
    'ultri;': number;
    'umacr;': number;
    'uogon;': number;
    'uplus;': number;
    'upsih;': number;
    'uring;': number;
    'urtri;': number;
    'utdot;': number;
    'utrif;': number;
    'uuarr;': number;
    'vBarv;': number;
    'vDash;': number;
    'varpi;': number;
    'vdash;': number;
    'veeeq;': number;
    'vltri;': number;
    'vnsub;': number;
    'vnsup;': number;
    'vprop;': number;
    'vrtri;': number;
    'wcirc;': number;
    'wedge;': number;
    'xcirc;': number;
    'xdtri;': number;
    'xhArr;': number;
    'xharr;': number;
    'xlArr;': number;
    'xlarr;': number;
    'xodot;': number;
    'xrArr;': number;
    'xrarr;': number;
    'xutri;': number;
    yacute: number;
    'ycirc;': number;
    AElig: number;
    Acirc: number;
    'Aopf;': number;
    Aring: number;
    'Ascr;': number;
    'Auml;': number;
    'Barv;': number;
    'Beta;': number;
    'Bopf;': number;
    'Bscr;': number;
    'CHcy;': number;
    'COPY;': number;
    'Cdot;': number;
    'Copf;': number;
    'Cscr;': number;
    'DJcy;': number;
    'DScy;': number;
    'DZcy;': number;
    'Darr;': number;
    'Dopf;': number;
    'Dscr;': number;
    Ecirc: number;
    'Edot;': number;
    'Eopf;': number;
    'Escr;': number;
    'Esim;': number;
    'Euml;': number;
    'Fopf;': number;
    'Fscr;': number;
    'GJcy;': number;
    'Gdot;': number;
    'Gopf;': number;
    'Gscr;': number;
    'Hopf;': number;
    'Hscr;': number;
    'IEcy;': number;
    'IOcy;': number;
    Icirc: number;
    'Idot;': number;
    'Iopf;': number;
    'Iota;': number;
    'Iscr;': number;
    'Iuml;': number;
    'Jopf;': number;
    'Jscr;': number;
    'KHcy;': number;
    'KJcy;': number;
    'Kopf;': number;
    'Kscr;': number;
    'LJcy;': number;
    'Lang;': number;
    'Larr;': number;
    'Lopf;': number;
    'Lscr;': number;
    'Mopf;': number;
    'Mscr;': number;
    'NJcy;': number;
    'Nopf;': number;
    'Nscr;': number;
    Ocirc: number;
    'Oopf;': number;
    'Oscr;': number;
    'Ouml;': number;
    'Popf;': number;
    'Pscr;': number;
    'QUOT;': number;
    'Qopf;': number;
    'Qscr;': number;
    'Rang;': number;
    'Rarr;': number;
    'Ropf;': number;
    'Rscr;': number;
    'SHcy;': number;
    'Sopf;': number;
    'Sqrt;': number;
    'Sscr;': number;
    'Star;': number;
    THORN: number;
    'TScy;': number;
    'Topf;': number;
    'Tscr;': number;
    'Uarr;': number;
    Ucirc: number;
    'Uopf;': number;
    'Upsi;': number;
    'Uscr;': number;
    'Uuml;': number;
    'Vbar;': number;
    'Vert;': number;
    'Vopf;': number;
    'Vscr;': number;
    'Wopf;': number;
    'Wscr;': number;
    'Xopf;': number;
    'Xscr;': number;
    'YAcy;': number;
    'YIcy;': number;
    'YUcy;': number;
    'Yopf;': number;
    'Yscr;': number;
    'Yuml;': number;
    'ZHcy;': number;
    'Zdot;': number;
    'Zeta;': number;
    'Zopf;': number;
    'Zscr;': number;
    acirc: number;
    acute: number;
    aelig: number;
    'andd;': number;
    'andv;': number;
    'ange;': number;
    'aopf;': number;
    'apid;': number;
    'apos;': number;
    aring: number;
    'ascr;': number;
    'auml;': number;
    'bNot;': number;
    'bbrk;': number;
    'beta;': number;
    'beth;': number;
    'bnot;': number;
    'bopf;': number;
    'boxH;': number;
    'boxV;': number;
    'boxh;': number;
    'boxv;': number;
    'bscr;': number;
    'bsim;': number;
    'bsol;': number;
    'bull;': number;
    'bump;': number;
    'caps;': number;
    'cdot;': number;
    cedil: number;
    'cent;': number;
    'chcy;': number;
    'cirE;': number;
    'circ;': number;
    'cire;': number;
    'comp;': number;
    'cong;': number;
    'copf;': number;
    'copy;': number;
    'cscr;': number;
    'csub;': number;
    'csup;': number;
    'cups;': number;
    'dArr;': number;
    'dHar;': number;
    'darr;': number;
    'dash;': number;
    'diam;': number;
    'djcy;': number;
    'dopf;': number;
    'dscr;': number;
    'dscy;': number;
    'dsol;': number;
    'dtri;': number;
    'dzcy;': number;
    'eDot;': number;
    'ecir;': number;
    ecirc: number;
    'edot;': number;
    'emsp;': number;
    'ensp;': number;
    'eopf;': number;
    'epar;': number;
    'epsi;': number;
    'escr;': number;
    'esim;': number;
    'euml;': number;
    'euro;': number;
    'excl;': number;
    'flat;': number;
    'fnof;': number;
    'fopf;': number;
    'fork;': number;
    'fscr;': number;
    'gdot;': number;
    'geqq;': number;
    'gesl;': number;
    'gjcy;': number;
    'gnap;': number;
    'gneq;': number;
    'gopf;': number;
    'gscr;': number;
    'gsim;': number;
    'gtcc;': number;
    'gvnE;': number;
    'hArr;': number;
    'half;': number;
    'harr;': number;
    'hbar;': number;
    'hopf;': number;
    'hscr;': number;
    icirc: number;
    'iecy;': number;
    iexcl: number;
    'imof;': number;
    'iocy;': number;
    'iopf;': number;
    'iota;': number;
    'iscr;': number;
    'isin;': number;
    'iuml;': number;
    'jopf;': number;
    'jscr;': number;
    'khcy;': number;
    'kjcy;': number;
    'kopf;': number;
    'kscr;': number;
    'lArr;': number;
    'lHar;': number;
    'lang;': number;
    laquo: number;
    'larr;': number;
    'late;': number;
    'lcub;': number;
    'ldca;': number;
    'ldsh;': number;
    'leqq;': number;
    'lesg;': number;
    'ljcy;': number;
    'lnap;': number;
    'lneq;': number;
    'lopf;': number;
    'lozf;': number;
    'lpar;': number;
    'lscr;': number;
    'lsim;': number;
    'lsqb;': number;
    'ltcc;': number;
    'ltri;': number;
    'lvnE;': number;
    'macr;': number;
    'male;': number;
    'malt;': number;
    micro: number;
    'mlcp;': number;
    'mldr;': number;
    'mopf;': number;
    'mscr;': number;
    'nGtv;': number;
    'nLtv;': number;
    'nang;': number;
    'napE;': number;
    'nbsp;': number;
    'ncap;': number;
    'ncup;': number;
    'ngeq;': number;
    'nges;': number;
    'ngtr;': number;
    'nisd;': number;
    'njcy;': number;
    'nldr;': number;
    'nleq;': number;
    'nles;': number;
    'nmid;': number;
    'nopf;': number;
    'npar;': number;
    'npre;': number;
    'nsce;': number;
    'nscr;': number;
    'nsim;': number;
    'nsub;': number;
    'nsup;': number;
    'ntgl;': number;
    'ntlg;': number;
    'nvap;': number;
    'nvge;': number;
    'nvgt;': number;
    'nvle;': number;
    'nvlt;': number;
    'oast;': number;
    'ocir;': number;
    ocirc: number;
    'odiv;': number;
    'odot;': number;
    'ogon;': number;
    'oint;': number;
    'omid;': number;
    'oopf;': number;
    'opar;': number;
    'ordf;': number;
    'ordm;': number;
    'oror;': number;
    'oscr;': number;
    'osol;': number;
    'ouml;': number;
    'para;': number;
    'part;': number;
    'perp;': number;
    'phiv;': number;
    'plus;': number;
    'popf;': number;
    pound: number;
    'prap;': number;
    'prec;': number;
    'prnE;': number;
    'prod;': number;
    'prop;': number;
    'pscr;': number;
    'qint;': number;
    'qopf;': number;
    'qscr;': number;
    'quot;': number;
    'rArr;': number;
    'rHar;': number;
    'race;': number;
    'rang;': number;
    raquo: number;
    'rarr;': number;
    'rcub;': number;
    'rdca;': number;
    'rdsh;': number;
    'real;': number;
    'rect;': number;
    'rhov;': number;
    'ring;': number;
    'ropf;': number;
    'rpar;': number;
    'rscr;': number;
    'rsqb;': number;
    'rtri;': number;
    'scap;': number;
    'scnE;': number;
    'sdot;': number;
    'sect;': number;
    'semi;': number;
    'sext;': number;
    'shcy;': number;
    'sime;': number;
    'simg;': number;
    'siml;': number;
    'smid;': number;
    'smte;': number;
    'solb;': number;
    'sopf;': number;
    'spar;': number;
    'squf;': number;
    'sscr;': number;
    'star;': number;
    'subE;': number;
    'sube;': number;
    'succ;': number;
    'sung;': number;
    'sup1;': number;
    'sup2;': number;
    'sup3;': number;
    'supE;': number;
    'supe;': number;
    szlig: number;
    'tbrk;': number;
    'tdot;': number;
    thorn: number;
    times: number;
    'tint;': number;
    'toea;': number;
    'topf;': number;
    'tosa;': number;
    'trie;': number;
    'tscr;': number;
    'tscy;': number;
    'uArr;': number;
    'uHar;': number;
    'uarr;': number;
    ucirc: number;
    'uopf;': number;
    'upsi;': number;
    'uscr;': number;
    'utri;': number;
    'uuml;': number;
    'vArr;': number;
    'vBar;': number;
    'varr;': number;
    'vert;': number;
    'vopf;': number;
    'vscr;': number;
    'wopf;': number;
    'wscr;': number;
    'xcap;': number;
    'xcup;': number;
    'xmap;': number;
    'xnis;': number;
    'xopf;': number;
    'xscr;': number;
    'xvee;': number;
    'yacy;': number;
    'yicy;': number;
    'yopf;': number;
    'yscr;': number;
    'yucy;': number;
    'yuml;': number;
    'zdot;': number;
    'zeta;': number;
    'zhcy;': number;
    'zopf;': number;
    'zscr;': number;
    'zwnj;': number;
    'AMP;': number;
    'Acy;': number;
    'Afr;': number;
    'And;': number;
    Auml: number;
    'Bcy;': number;
    'Bfr;': number;
    COPY: number;
    'Cap;': number;
    'Cfr;': number;
    'Chi;': number;
    'Cup;': number;
    'Dcy;': number;
    'Del;': number;
    'Dfr;': number;
    'Dot;': number;
    'ENG;': number;
    'ETH;': number;
    'Ecy;': number;
    'Efr;': number;
    'Eta;': number;
    Euml: number;
    'Fcy;': number;
    'Ffr;': number;
    'Gcy;': number;
    'Gfr;': number;
    'Hat;': number;
    'Hfr;': number;
    'Icy;': number;
    'Ifr;': number;
    'Int;': number;
    Iuml: number;
    'Jcy;': number;
    'Jfr;': number;
    'Kcy;': number;
    'Kfr;': number;
    'Lcy;': number;
    'Lfr;': number;
    'Lsh;': number;
    'Map;': number;
    'Mcy;': number;
    'Mfr;': number;
    'Ncy;': number;
    'Nfr;': number;
    'Not;': number;
    'Ocy;': number;
    'Ofr;': number;
    Ouml: number;
    'Pcy;': number;
    'Pfr;': number;
    'Phi;': number;
    'Psi;': number;
    QUOT: number;
    'Qfr;': number;
    'REG;': number;
    'Rcy;': number;
    'Rfr;': number;
    'Rho;': number;
    'Rsh;': number;
    'Scy;': number;
    'Sfr;': number;
    'Sub;': number;
    'Sum;': number;
    'Sup;': number;
    'Tab;': number;
    'Tau;': number;
    'Tcy;': number;
    'Tfr;': number;
    'Ucy;': number;
    'Ufr;': number;
    Uuml: number;
    'Vcy;': number;
    'Vee;': number;
    'Vfr;': number;
    'Wfr;': number;
    'Xfr;': number;
    'Ycy;': number;
    'Yfr;': number;
    'Zcy;': number;
    'Zfr;': number;
    'acE;': number;
    'acd;': number;
    'acy;': number;
    'afr;': number;
    'amp;': number;
    'and;': number;
    'ang;': number;
    'apE;': number;
    'ape;': number;
    'ast;': number;
    auml: number;
    'bcy;': number;
    'bfr;': number;
    'bne;': number;
    'bot;': number;
    'cap;': number;
    cent: number;
    'cfr;': number;
    'chi;': number;
    'cir;': number;
    copy: number;
    'cup;': number;
    'dcy;': number;
    'deg;': number;
    'dfr;': number;
    'die;': number;
    'div;': number;
    'dot;': number;
    'ecy;': number;
    'efr;': number;
    'egs;': number;
    'ell;': number;
    'els;': number;
    'eng;': number;
    'eta;': number;
    'eth;': number;
    euml: number;
    'fcy;': number;
    'ffr;': number;
    'gEl;': number;
    'gap;': number;
    'gcy;': number;
    'gel;': number;
    'geq;': number;
    'ges;': number;
    'gfr;': number;
    'ggg;': number;
    'glE;': number;
    'gla;': number;
    'glj;': number;
    'gnE;': number;
    'gne;': number;
    'hfr;': number;
    'icy;': number;
    'iff;': number;
    'ifr;': number;
    'int;': number;
    iuml: number;
    'jcy;': number;
    'jfr;': number;
    'kcy;': number;
    'kfr;': number;
    'lEg;': number;
    'lap;': number;
    'lat;': number;
    'lcy;': number;
    'leg;': number;
    'leq;': number;
    'les;': number;
    'lfr;': number;
    'lgE;': number;
    'lnE;': number;
    'lne;': number;
    'loz;': number;
    'lrm;': number;
    'lsh;': number;
    macr: number;
    'map;': number;
    'mcy;': number;
    'mfr;': number;
    'mho;': number;
    'mid;': number;
    'nGg;': number;
    'nGt;': number;
    'nLl;': number;
    'nLt;': number;
    'nap;': number;
    nbsp: number;
    'ncy;': number;
    'nfr;': number;
    'ngE;': number;
    'nge;': number;
    'ngt;': number;
    'nis;': number;
    'niv;': number;
    'nlE;': number;
    'nle;': number;
    'nlt;': number;
    'not;': number;
    'npr;': number;
    'nsc;': number;
    'num;': number;
    'ocy;': number;
    'ofr;': number;
    'ogt;': number;
    'ohm;': number;
    'olt;': number;
    'ord;': number;
    ordf: number;
    ordm: number;
    'orv;': number;
    ouml: number;
    'par;': number;
    para: number;
    'pcy;': number;
    'pfr;': number;
    'phi;': number;
    'piv;': number;
    'prE;': number;
    'pre;': number;
    'psi;': number;
    'qfr;': number;
    quot: number;
    'rcy;': number;
    'reg;': number;
    'rfr;': number;
    'rho;': number;
    'rlm;': number;
    'rsh;': number;
    'scE;': number;
    'sce;': number;
    'scy;': number;
    sect: number;
    'sfr;': number;
    'shy;': number;
    'sim;': number;
    'smt;': number;
    'sol;': number;
    'squ;': number;
    'sub;': number;
    'sum;': number;
    sup1: number;
    sup2: number;
    sup3: number;
    'sup;': number;
    'tau;': number;
    'tcy;': number;
    'tfr;': number;
    'top;': number;
    'ucy;': number;
    'ufr;': number;
    'uml;': number;
    uuml: number;
    'vcy;': number;
    'vee;': number;
    'vfr;': number;
    'wfr;': number;
    'xfr;': number;
    'ycy;': number;
    'yen;': number;
    'yfr;': number;
    yuml: number;
    'zcy;': number;
    'zfr;': number;
    'zwj;': number;
    AMP: number;
    'DD;': number;
    ETH: number;
    'GT;': number;
    'Gg;': number;
    'Gt;': number;
    'Im;': number;
    'LT;': number;
    'Ll;': number;
    'Lt;': number;
    'Mu;': number;
    'Nu;': number;
    'Or;': number;
    'Pi;': number;
    'Pr;': number;
    REG: number;
    'Re;': number;
    'Sc;': number;
    'Xi;': number;
    'ac;': number;
    'af;': number;
    amp: number;
    'ap;': number;
    'dd;': number;
    deg: number;
    'ee;': number;
    'eg;': number;
    'el;': number;
    eth: number;
    'gE;': number;
    'ge;': number;
    'gg;': number;
    'gl;': number;
    'gt;': number;
    'ic;': number;
    'ii;': number;
    'in;': number;
    'it;': number;
    'lE;': number;
    'le;': number;
    'lg;': number;
    'll;': number;
    'lt;': number;
    'mp;': number;
    'mu;': number;
    'ne;': number;
    'ni;': number;
    not: number;
    'nu;': number;
    'oS;': number;
    'or;': number;
    'pi;': number;
    'pm;': number;
    'pr;': number;
    reg: number;
    'rx;': number;
    'sc;': number;
    shy: number;
    uml: number;
    'wp;': number;
    'wr;': number;
    'xi;': number;
    yen: number;
    GT: number;
    LT: number;
    gt: number;
    lt: number;
};
export default _default;
