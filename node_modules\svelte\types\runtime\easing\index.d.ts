export { identity as linear } from 'svelte/internal';
export declare function backInOut(t: number): number;
export declare function backIn(t: number): number;
export declare function backOut(t: number): number;
export declare function bounceOut(t: number): number;
export declare function bounceInOut(t: number): number;
export declare function bounceIn(t: number): number;
export declare function circInOut(t: number): number;
export declare function circIn(t: number): number;
export declare function circOut(t: number): number;
export declare function cubicInOut(t: number): number;
export declare function cubicIn(t: number): number;
export declare function cubicOut(t: number): number;
export declare function elasticInOut(t: number): number;
export declare function elasticIn(t: number): number;
export declare function elasticOut(t: number): number;
export declare function expoInOut(t: number): number;
export declare function expoIn(t: number): number;
export declare function expoOut(t: number): number;
export declare function quadInOut(t: number): number;
export declare function quadIn(t: number): number;
export declare function quadOut(t: number): number;
export declare function quartInOut(t: number): number;
export declare function quartIn(t: number): number;
export declare function quartOut(t: number): number;
export declare function quintInOut(t: number): number;
export declare function quintIn(t: number): number;
export declare function quintOut(t: number): number;
export declare function sineInOut(t: number): number;
export declare function sineIn(t: number): number;
export declare function sineOut(t: number): number;
