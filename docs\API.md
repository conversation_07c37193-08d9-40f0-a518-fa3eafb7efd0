# Daily Grind Dash - API Documentation

## Game State Structure

### GameState
```typescript
interface GameState {
  players: { [id: string]: Player };
  currentPlayer: string;
  phase: 'setup' | 'roll' | 'move' | 'resolve' | 'deal' | 'income' | 'victory';
  turn: number;
  round: number;
  board: BoardTile[];
  decks: { market: Card[]; sideHustle: Card[]; grandOpportunity: Card[]; gizmo: Card[] };
  discardPiles: { market: Card[]; sideHustle: Card[]; grandOpportunity: Card[]; gizmo: Card[] };
  activeDeals: Deal[];
  chatMessages: ChatMessage[];
  gameMode: 'local' | 'online';
  winner?: string;
}
```

## Game Moves

### joinGame(playerName: string, professionName: string)
- **Phase**: setup
- **Description**: Join the game with a chosen profession
- **Parameters**:
  - `playerName`: Display name for the player
  - `professionName`: One of 'Teacher', 'Engineer', 'Nurse', 'Sales Rep', 'Artist', 'Mechanic'

### rollDice()
- **Phase**: roll
- **Description**: Roll dice in online mode (uses boardgame.io RNG)
- **Returns**: `{ roll: number, newPosition: number }`

### selectTile(tileId: number)
- **Phase**: roll
- **Description**: Select tile in local mode (manual position selection)
- **Parameters**:
  - `tileId`: Board position (0-19)

### drawCard(deckType: string)
- **Phase**: resolve
- **Description**: Draw a card from the specified deck
- **Parameters**:
  - `deckType`: 'market', 'sideHustle', 'grandOpportunity', or 'gizmo'

### sendMessage(type: string, message: string, recipientId?: string, dealId?: string)
- **Phase**: any
- **Description**: Send a chat message
- **Parameters**:
  - `type`: 'global', 'deal', or 'direct'
  - `message`: Message content
  - `recipientId`: Target player for direct messages
  - `dealId`: Associated deal for deal-specific chats

### endTurn()
- **Phase**: resolve
- **Description**: End current player's turn, apply income/expenses, check victory

## Game Phases

1. **setup**: Players join and select professions
2. **roll**: Current player rolls dice or selects tile
3. **resolve**: Handle tile effects, draw cards, make deals
4. **victory**: Game over, winner declared

## Victory Condition
Game ends when any player's `passiveIncome >= expenses`
